<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper">

<select id="selEvalProjectData" resultType="com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones">
    SELECT
        project_id,
        MAX( expected_complete_date ) AS expectedCompleteDate,
        MAX( actual_complete_date ) AS actualCompleteDate
    FROM
        project_business_milestones
    WHERE
        del_flag = 0
    <if test="projectIds != null and projectIds.size() > 0">
        AND project_id IN
        <foreach collection="projectIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </if>
    GROUP BY
        project_id;
    </select>

</mapper>
