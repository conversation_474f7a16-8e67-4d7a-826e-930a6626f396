<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostPresalesTaskConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="task_name" property="taskName"/>
        <result column="task_category" property="taskCategory"/>
        <result column="task_level" property="taskLevel"/>
        <result column="task_desc" property="taskDesc"/>
        <result column="disassembly_type" property="disassemblyType"/>
        <result column="manager_type" property="managerType"/>
        <result column="manager_role" property="managerRole"/>
        <result column="manager_id" property="managerId"/>
        <result column="manager_name" property="managerName"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, task_name, task_category, task_level, task_desc, disassembly_type, manager_type, manager_role, manager_id, manager_name, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getNotExistDeliverTaskProjectList" resultType="com.gok.pboot.pms.entity.domain.ProjectInfo">
        SELECT
            pi.id,
            pi.item_name,
            pi.project_salesperson,
            pi.salesman_user_id,
            pi.manager_user_id,
            pi.manager_user_name,
            pi.pre_sale_user_id,
            pi.pre_sale_user_name
        FROM project_info pi
                 LEFT JOIN (
            SELECT
                project_id,
                MAX(CASE WHEN default_conf = 1 THEN 1 ELSE 0 END) as has_default_conf
            FROM cost_deliver_task
            WHERE del_flag = 0
            GROUP BY project_id
        ) cdt ON pi.id = cdt.project_id
        WHERE pi.project_status IN (0, 2)
          AND (cdt.project_id IS NULL OR cdt.has_default_conf = 0)
    </select>

</mapper> 