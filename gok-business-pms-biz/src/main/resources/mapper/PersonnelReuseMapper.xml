<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.PersonnelReuseMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.PersonnelReuse">
        <id column="id" property="id"/>
        <result column="reuseDate" property="reuseDate"/>
        <result column="projectId" property="projectId"/>
        <result column="projectCode" property="projectCode"/>
        <result column="projectName" property="projectName"/>
        <result column="userRealName" property="userRealName"/>
        <result column="aggregatedDays" property="aggregatedDays"/>
        <result column="personnelType" property="personnelType"/>
        <result column="grade" property="grade"/>
        <result column="schoolName" property="schoolName"/>
        <result column="major" property="major"/>
        <result column="mobile" property="mobile"/>
        <result column="remark" property="remark"/>
        <result column="executorUserId" property="executorUserId"/>
        <result column="executorUserRealName" property="executorUserRealName"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
        <result column="approvalStatus" property="approvalStatus"/>
        <result column="approvalReason" property="approvalReason"/>
    </resultMap>


    <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
					a.reuse_date AS 'reuseDate',
					a.project_id AS 'projectId',
					a.project_code AS 'projectCode',
					a.project_name AS 'projectName',
					a.user_real_name AS 'userRealName',
					a.aggregated_days AS 'aggregatedDays',
					a.personnel_type AS 'personnelType',
					a.grade AS 'grade',
					a.school_name AS 'schoolName',
					a.major AS 'major',
					a.mobile AS 'mobile',
					a.remark AS 'remark',
					a.executor_user_id AS 'executorUserId',
					a.executor_user_real_name AS 'executorUserRealName',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag',
                    a.approval_status AS 'approvalStatus',
                    a.approval_reason AS 'approvalReason'
        </sql>

    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.PersonnelReuse">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_personnel_reuse a
        <where>
            a.del_flag = 0
            <if test="filter.projectName !=null and filter.projectName != '' ">
                AND a.project_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.reuseDate != null and filter.reuseDate != ''">
                AND DATE_FORMAT(a.reuse_date, '%Y-%m-%d') = DATE_FORMAT('${filter.reuseDate}-01','%Y-%m-%d')
            </if>
            <if test="filter.userId != null ">
                AND
                (
                a.executor_user_id = #{filter.userId}
                <if test="filter.projectIds != null and filter.projectIds.size() > 0">
                    OR a.project_id IN
                    <foreach collection="filter.projectIds" item="projectId" open="(" separator="," close=")">
                        #{projectId}
                    </foreach>
                </if>
                )
            </if>
        </where>
        ORDER BY  FIELD(a.approval_status,3,2,4) asc, a.ctime desc, a.id asc
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_personnel_reuse SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_personnel_reuse (
                id,
                reuse_date,
                project_id,
                project_code,
                project_name,
                user_real_name,
                aggregated_days,
                personnel_type,
                grade,
                school_name,
                major,
                mobile,
                remark,
                executor_user_id,
                executor_user_real_name,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.reuseDate},
                    #{item.projectId},
                    #{item.projectCode},
                    #{item.projectName},
                    #{item.userRealName},
                    #{item.aggregatedDays},
                    #{item.personnelType},
                    #{item.grade},
                    #{item.schoolName},
                    #{item.major},
                    #{item.mobile},
                    #{item.remark},
                    #{item.executorUserId},
                    #{item.executorUserRealName},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_personnel_reuse set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_personnel_reuse  SET
                    id = #{item.id},
                    reuse_date = #{item.reuseDate},
                    project_id = #{item.projectId},
                    project_code = #{item.projectCode},
                    project_name = #{item.projectName},
                    user_real_name = #{item.userRealName},
                    aggregated_days = #{item.aggregatedDays},
                    personnel_type = #{item.personnelType},
                    grade = #{item.grade},
                    school_name = #{item.schoolName},
                    major = #{item.major},
                    mobile = #{item.mobile},
                    remark = #{item.remark},
                    executor_user_id = #{item.executorUserId},
                    executor_user_real_name = #{item.executorUserRealName},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_personnel_reuse
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" reuse_date =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.reuseDate!=null">
                                when id=#{item.id} then #{item.reuseDate}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" project_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.projectId!=null">
                                when id=#{item.id} then #{item.projectId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" project_code =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.projectCode!=null">
                                when id=#{item.id} then #{item.projectCode}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" project_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.projectName!=null">
                                when id=#{item.id} then #{item.projectName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" user_real_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userRealName!=null">
                                when id=#{item.id} then #{item.userRealName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" aggregated_days =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.aggregatedDays!=null">
                                when id=#{item.id} then #{item.aggregatedDays}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" personnel_type =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.personnelType!=null">
                                when id=#{item.id} then #{item.personnelType}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" grade =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.grade!=null">
                                when id=#{item.id} then #{item.grade}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" school_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.schoolName!=null">
                                when id=#{item.id} then #{item.schoolName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" major =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.major!=null">
                                when id=#{item.id} then #{item.major}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mobile =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mobile!=null">
                                when id=#{item.id} then #{item.mobile}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" remark =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.remark!=null">
                                when id=#{item.id} then #{item.remark}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" executor_user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.executorUserId!=null">
                                when id=#{item.id} then #{item.executorUserId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" executor_user_real_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.executorUserRealName!=null">
                                when id=#{item.id} then #{item.executorUserRealName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="personnelReuseIdPage" resultType="java.lang.Long">
        SELECT
        mpr.id AS 'id',
        mp.first_level_department_id AS 'dept_id'
        FROM mhour_personnel_reuse mpr
        LEFT JOIN project_info mp ON mpr.project_id = mp.id
        <where>
            mpr.del_flag = 0
            <if test="personnelReuseFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(mpr.reuse_date, '%Y%m') = DATE_FORMAT('${personnelReuseFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="personnelReuseFindPageDTO.exportName != null and personnelReuseFindPageDTO.exportName != '' ">
                AND mpr.executor_user_real_name LIKE concat('%',#{personnelReuseFindPageDTO.exportName},'%')
            </if>
            <if test="personnelReuseFindPageDTO.projectId != null">
                AND mpr.project_id = #{personnelReuseFindPageDTO.projectId}
            </if>
            <if test="personnelReuseFindPageDTO.projectName != null">
                <bind name="projectNameLike" value="'%' + personnelReuseFindPageDTO.projectName + '%'"/>
                AND mpr.project_name LIKE #{projectNameLike}
            </if>
            <if test="approvalStatus != null">
                AND mpr.approval_status = #{approvalStatus}
            </if>
        </where>
    </select>

    <select id="personnelReuseFindPage" resultType="com.gok.pboot.pms.entity.vo.PersonnelReuseFindPageVO">
        SELECT
        mpr.project_name AS 'projectName',
        mpr.grade AS 'grade',
        mpr.user_real_name AS 'userRealName',
        mpr.aggregated_days AS 'aggregatedDays',
        mpr.personnel_type AS 'personnelType',
        mpr.school_name AS 'schoolName',
        mpr.major AS 'major',
        mpr.mobile AS 'mobile',
        mpr.remark AS 'remark',
        mpr.executor_user_real_name AS 'executorUserRealName'
        FROM mhour_personnel_reuse mpr
        <where>
            mpr.del_flag = 0
            <if test="personnelReuseFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(mpr.reuse_date, '%Y%m') = DATE_FORMAT('${personnelReuseFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="personnelReuseFindPageDTO.exportName != null and personnelReuseFindPageDTO.exportName != '' ">
                AND mpr.executor_user_real_name   LIKE concat('%',#{personnelReuseFindPageDTO.exportName},'%')
            </if>
            <if test="personnelReuseFindPageDTO.projectId != null">
                AND mpr.project_id = #{personnelReuseFindPageDTO.projectId}
            </if>
            <if test="personnelReuseFindPageDTO.projectName != null">
                <bind name="projectNameLike" value="'%' + personnelReuseFindPageDTO.projectName + '%'"/>
                AND mpr.project_name LIKE #{projectNameLike}
            </if>
            <if test="approvalStatus != null">
                AND mpr.approval_status = #{approvalStatus}
            </if>
            AND (
             mpr.executor_user_id = #{personnelReuseFindPageDTO.userId}
            <if test="personnelReuseFindPageDTO.ids != null and personnelReuseFindPageDTO.ids.size() > 0">
                OR mpr.id in
                <foreach collection="personnelReuseFindPageDTO.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="personnelReuseFindPageDTO.projectIds != null and personnelReuseFindPageDTO.projectIds.size() > 0">
                OR mpr.project_id in
                <foreach collection="personnelReuseFindPageDTO.projectIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )

        </where>
        ORDER BY mpr.project_name
    </select>

    <!--<select id="personnelReuseFindPage" resultType="com.gok.pboot.pms.entity.vo.PersonnelReuseFindPageVO">-->
    <!--    SELECT * FROM-->
    <!--    (SELECT-->
    <!--    mpr.project_name AS 'projectName',-->
    <!--    mpr.grade AS 'grade',-->
    <!--    mpr.user_real_name AS 'userRealName',-->
    <!--    mpr.aggregated_days AS 'aggregatedDays',-->
    <!--    mpr.personnel_type AS 'personnelType',-->
    <!--    mpr.school_name AS 'schoolName',-->
    <!--    mpr.major AS 'major',-->
    <!--    mpr.mobile AS 'mobile',-->
    <!--    mpr.remark AS 'remark',-->
    <!--    mpr.executor_user_real_name AS 'executorUserRealName'-->
    <!--    FROM mhour_personnel_reuse mpr-->
    <!--    <where>-->
    <!--        mpr.del_flag = 0-->
    <!--        <if test="personnelReuseFindPageDTO.selectMonth != null">-->
    <!--            AND DATE_FORMAT(mpr.reuse_date, '%Y%m') = DATE_FORMAT('${personnelReuseFindPageDTO.selectMonth}-01','%Y%m')-->
    <!--        </if>-->
    <!--        <if test="personnelReuseFindPageDTO.exportName != null and personnelReuseFindPageDTO.exportName != '' ">-->
    <!--            AND mpr.executor_user_real_name   LIKE concat('%',#{personnelReuseFindPageDTO.exportName},'%')-->
    <!--        </if>-->
    <!--        <if test="personnelReuseFindPageDTO.projectId != null">-->
    <!--            AND mpr.project_id = #{personnelReuseFindPageDTO.projectId}-->
    <!--        </if>-->
    <!--        <if test="approvalStatus != null">-->
    <!--            AND mpr.approval_status = #{approvalStatus}-->
    <!--        </if>-->
    <!--        AND (-->
    <!--        mpr.executor_user_id = #{personnelReuseFindPageDTO.userId}-->
    <!--        <if test="personnelReuseFindPageDTO.ids != null and personnelReuseFindPageDTO.ids.size() > 0">-->
    <!--            OR mpr.id in-->
    <!--            <foreach collection="personnelReuseFindPageDTO.ids" item="id" open="(" separator="," close=")">-->
    <!--                #{id}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        )-->
    <!--    </where>-->
    <!--    union distinct-->
    <!--    SELECT-->
    <!--    mpr.project_name AS 'projectName',-->
    <!--    mpr.grade AS 'grade',-->
    <!--    mpr.user_real_name AS 'userRealName',-->
    <!--    mpr.aggregated_days AS 'aggregatedDays',-->
    <!--    mpr.personnel_type AS 'personnelType',-->
    <!--    mpr.school_name AS 'schoolName',-->
    <!--    mpr.major AS 'major',-->
    <!--    mpr.mobile AS 'mobile',-->
    <!--    mpr.remark AS 'remark',-->
    <!--    mpr.executor_user_real_name AS 'executorUserRealName'-->
    <!--    FROM mhour_personnel_reuse mpr-->
    <!--    <where>-->
    <!--        mpr.del_flag = 0-->
    <!--        <if test="personnelReuseFindPageDTO.selectMonth != null">-->
    <!--            AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =-->
    <!--            DATE_FORMAT('${personnelReuseFindPageDTO.selectMonth}-01','%Y%m')-->
    <!--        </if>-->
    <!--        <if test="personnelReuseFindPageDTO.exportName != null and personnelReuseFindPageDTO.exportName != '' ">-->
    <!--            AND mpr.executor_user_real_name LIKE concat('%',#{personnelReuseFindPageDTO.exportName},'%')-->
    <!--        </if>-->
    <!--        <if test="personnelReuseFindPageDTO.projectId != null">-->
    <!--            AND mpr.project_id = #{personnelReuseFindPageDTO.projectId}-->
    <!--        </if>-->
    <!--        <if test="approvalStatus != null">-->
    <!--            AND mpr.approval_status = #{approvalStatus}-->
    <!--        </if>-->
    <!--        <if test="personnelReuseFindPageDTO.projectIds != null and personnelReuseFindPageDTO.projectIds.size() > 0">-->
    <!--            AND mpr.project_id in-->
    <!--            <foreach collection="personnelReuseFindPageDTO.projectIds" item="id" open="(" separator="," close=")">-->
    <!--                #{id}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--    </where>) t1-->
    <!--    ORDER BY t1.projectName-->
    <!--</select>-->

    <select id="selectDailyPaperById" resultType="com.gok.pboot.pms.entity.dto.DailyPaperCommonDTO">
        select id as dailyPaperId,project_id, project_name, reuse_date as submissionDate, executor_user_id as userId,aggregated_days as aggregatedDays
        from mhour_personnel_reuse
        where del_flag = 0
          and id = #{id}
    </select>

    <select id="findReuseAndDeliveryView"
            resultType="com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewBO"
    >
        SELECT
            id,
            userRealName,
            monthDate,
            aggregatedDays,
            type,
            approvalStatus,
            approvalName,
            normalWorkDays,
            ompensatoryDays,
            restWorkDays,
            holidaysWorkDays
        FROM (
            SELECT
                pr.id AS id,
                pr.user_real_name AS userRealName,
                pr.reuse_date AS monthDate,
                pr.aggregated_days AS aggregatedDays,
                '人才复用' AS type,
                pr.approval_status AS approvalStatus,
                pr.modifier AS approvalName,
                  0  normalWorkDays,
                  0  ompensatoryDays,
                  0  restWorkDays,
                  0  holidaysWorkDays
            FROM mhour_personnel_reuse pr
            LEFT JOIN mhour_filing f ON
                f.`year` = DATE_FORMAT(pr.reuse_date, '%Y')
                AND
                f.`month` = DATE_FORMAT(pr.reuse_date, '%m')
            WHERE
                pr.project_id = #{filter.projectId}
            AND
                f.del_flag = 0
            AND
                f.filed != ${@<EMAIL>}
            AND
                pr.del_flag = 0
            <if test="filter.approvalStatus != null">
                AND pr.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.startTime != null">
                AND pr.reuse_date &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND pr.reuse_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.username != null">
                <bind name="usernameLike" value="'%' + filter.username + '%'"/>
                AND pr.user_real_name LIKE #{usernameLike}
            </if>

            UNION ALL

            SELECT
                pdh.id AS id,
                pdh.user_real_name AS userRealName,
                pdh.reuse_date AS monthDate,
                IFNULL(pdh.project_consumed , 0) AS aggregatedDays,
                '交付人员' AS type,
                pdh.approval_status AS approvalStatus,
                pdh.modifier AS approvalName,
                pdh.normal_work_days AS normalWorkDays,
                pdh.ompensatory_days AS ompensatoryDays,
                pdh.rest_work_days AS restWorkDays,
                pdh.holidays_work_days AS holidaysWorkDays
            FROM mhour_personnel_delivery_hour pdh
            LEFT JOIN mhour_filing f ON
                f.`year` = DATE_FORMAT(pdh.reuse_date, '%Y')
                AND
                f.`month` = DATE_FORMAT(pdh.reuse_date, '%m')
            WHERE
                pdh.project_id = #{filter.projectId}
                AND f.del_flag = 0
                AND f.filed != ${@<EMAIL>}
                AND pdh.del_flag = 0
            <if test="filter.approvalStatus != null">
                AND pdh.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.startTime != null">
                AND pdh.reuse_date &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND pdh.reuse_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.username != null">
                <bind name="usernameLike" value="'%' + filter.username + '%'"/>
                AND pdh.user_real_name LIKE #{usernameLike}
            </if>
        ) r
    </select>

    <select id="findReuseAndDeliveryViewTotal"
            resultType="com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewTotalBO">
        SELECT
            r.projectId AS projectId,
            r.projectName AS projectName,
            IFNULL(COUNT(r.personnelId), 0) AS approvalNum,
            IFNULL(SUM(r.aggregatedDays), 0) AS aggregatedDays
        FROM (
        SELECT
            pi.id AS projectId,
            pi.item_name AS projectName,
            pr.id AS personnelId,
            IFNULL(pr.aggregated_days, 0) AS aggregatedDays
        FROM project_info pi
        LEFT JOIN mhour_personnel_reuse pr ON pi.id = pr.project_id
        LEFT JOIN mhour_filing f ON
            f.`year` = DATE_FORMAT(pr.reuse_date, '%Y')
        AND
            f.`month` = DATE_FORMAT(pr.reuse_date, '%m')
        WHERE
            pr.del_flag = 0
        AND
            f.del_flag = 0
        AND
            f.filed != ${@<EMAIL>}
            <if test="filter.approvalStatus != null">
              AND pr.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.startTime != null">
              AND pr.reuse_date &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND pr.reuse_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.username != null">
                <bind name="usernameLike" value="'%' + filter.username + '%'"/>
                AND pr.user_real_name LIKE #{usernameLike}
            </if>
            <if test="filter.projectId != null">
                AND pi.id = #{filter.projectId}
            </if>

            UNION ALL

            SELECT
                pi.id AS projectId,
                pi.item_name AS projectName,
                pdh.id AS personnelId,
                IFNULL(pdh.project_consumed, 0) AS aggregatedDays
            FROM project_info pi
            LEFT JOIN mhour_personnel_delivery_hour pdh ON pi.id = pdh.project_id
            LEFT JOIN mhour_filing f ON
                f.`year` = DATE_FORMAT(pdh.reuse_date, '%Y')
                AND
                f.`month` = DATE_FORMAT(pdh.reuse_date, '%m')
            WHERE
                pdh.del_flag = 0
            AND
                f.del_flag = 0
            AND
                f.filed != ${@<EMAIL>}
            AND
                pdh.del_flag = 0
            <if test="filter.approvalStatus != null">
                AND pdh.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.startTime != null">
                AND pdh.reuse_date &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND pdh.reuse_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.username != null">
                <bind name="usernameLike" value="'%' + filter.username + '%'"/>
                AND pdh.user_real_name LIKE #{usernameLike}
            </if>
            <if test="filter.projectId != null">
                AND pi.id = #{filter.projectId}
            </if>
        ) r
    </select>

    <update id="updateById">
        UPDATE mhour_personnel_reuse
        SET
        aggregated_days = #{personnelReuseUpdateDTO.aggregatedDays},
        remark = #{personnelReuseUpdateDTO.remark},
        modifier = #{personnelReuseUpdateDTO.modifier},
        modifier_id = #{personnelReuseUpdateDTO.modifierId},
        mtime = #{personnelReuseUpdateDTO.mtime},
        approval_status = #{personnelReuseUpdateDTO.approvalStatus}
        WHERE
        id = #{personnelReuseUpdateDTO.id}
    </update>

    <update id="updateApprovalStatusById">
        UPDATE mhour_personnel_reuse
        SET
        approval_status = #{param.approvalStatus},
        modifier = #{param.approvalName},
        <if test="param.approvalReason!= null and param.approvalReason != '' ">
            approval_reason = #{param.approvalReason},
        </if>
        modifier_id = #{param.approvalId},
        mtime = #{param.mtime}
        WHERE
        id = #{param.id}
    </update>

    <update id="updateApprovalStatusByIds">
        UPDATE mhour_personnel_reuse
        SET
        approval_status = 4,
        modifier = #{auditName},
        modifier_id = #{auditId},
        mtime = #{mTime}
        WHERE
        id in
        <foreach collection="reuseIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findPage" resultType="com.gok.pboot.pms.entity.vo.PersonnelReusePageVO">
        SELECT
        <include refid="Base_Column_List"/>,
        b.manager_user_name as managerUserName
        FROM mhour_personnel_reuse a
        left join project_info b on a.project_id = b.id
        <where>
            a.del_flag = 0
            <if test="filter.projectName !=null and filter.projectName != '' ">
                AND a.project_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.reuseDate != null and filter.reuseDate != ''">
                AND DATE_FORMAT(a.reuse_date, '%Y-%m-%d') = DATE_FORMAT('${filter.reuseDate}-01','%Y-%m-%d')
            </if>
            <if test="filter.userId != null ">
                AND
                (
                a.executor_user_id = #{filter.userId}
                <if test="filter.projectIds != null and filter.projectIds.size() > 0">
                    OR a.project_id IN
                    <foreach collection="filter.projectIds" item="projectId" open="(" separator="," close=")">
                        #{projectId}
                    </foreach>
                </if>
                )
            </if>
        </where>
        ORDER BY  FIELD(a.approval_status,3,2,4) asc, a.ctime desc, a.id asc
    </select>
</mapper>
