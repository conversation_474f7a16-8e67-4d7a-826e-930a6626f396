<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectSystemProcessInfoMapper">

    <select id="findListPage" resultMap="BaseResultMapProjectRiskFindPageVo">
        SELECT
        id,
        request_id,
        project_id,
        applicat_id,
        applicat,
        process_type,
        status,
        `name`,
        ctime
        FROM project_system_process_info
        <where>1=1
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY ctime DESC
    </select>

    <!--批量插入语句-->
    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO project_system_process_info(
        id,
        request_id,
        project_id,
        applicat_id,
        applicat,
        process_type,
        status,
        `name`,
        ctime
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.requestId},
            #{item.projectId},
            #{item.applicatId},
            #{item.applicat},
            #{item.processType},
            #{item.status},
            #{item.name},
            #{item.ctime}
            )
        </foreach>
    </insert>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectSystemProcessInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="project_id" property="projectId"/>
        <result column="applicat_id" property="applicatId"/>
        <result column="applicat" property="applicat"/>
        <result column="process_type" property="processType"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

    <!-- 分页询映射结果 -->
    <resultMap id="BaseResultMapProjectRiskFindPageVo" type="com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="project_id" property="projectId"/>
        <result column="applicat_id" property="applicatId"/>
        <result column="applicat" property="applicat"/>
        <result column="process_type" property="processType"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

</mapper>