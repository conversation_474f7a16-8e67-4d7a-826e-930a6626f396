<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.PersonnelDeliveryHourMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.PersonnelDeliveryHour">
        <id column="id" property="id"/>
        <result column="reuse_date" property="reuseDate"/>
        <result column="project_id" property="projectId"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_status" property="projectStatus"/>
        <result column="revenue_dept_id" property="revenueDeptId"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="work_code" property="workCode"/>
        <result column="cw_due_attendance" property="cwDueAttendance"/>
        <result column="attendance_days" property="attendanceDays"/>
        <result column="project_consumed" property="projectConsumed"/>
        <result column="gross_pay" property="grossPay"/>
        <result column="social_security" property="socialSecurity"/>
        <result column="accumulation_fund" property="accumulationFund"/>
        <result column="wage_cost" property="wageCost"/>
        <result column="social_security_cost" property="socialSecurityCost"/>
        <result column="accumulation_fund_cost" property="accumulationFundCost"/>
        <result column="total" property="total"/>
        <result column="total_project_cost" property="totalProjectCost"/>
        <result column="paying_place" property="payingPlace"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="remark" property="remark"/>
        <result column="executor_user_id" property="executorUserId"/>
        <result column="executor_user_real_name" property="executorUserRealName"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_reason" property="approvalReason"/>
    </resultMap>
    <sql id="Base_Column_List">
        mpdh.id,
        mpdh.reuse_date,
        mpdh.project_id,
        mpdh.project_code,
        mpdh.project_name,
        mpdh.project_status,
        mpdh.revenue_dept_id,
        mpdh.user_real_name,
        mpdh.work_code,
        mpdh.cw_due_attendance,
        mpdh.attendance_days,
        mpdh.project_consumed,
        mpdh.gross_pay,
        mpdh.social_security,
        mpdh.accumulation_fund,
        mpdh.wage_cost,
        mpdh.social_security_cost,
        mpdh.accumulation_fund_cost,
        mpdh.total,
        mpdh.total_project_cost,
        mpdh.paying_place,
        mpdh.dept_id,
        mpdh.dept_name,
        mpdh.remark,
        mpdh.executor_user_id,
        mpdh.executor_user_real_name,
        mpdh.creator,
        mpdh.creator_id,
        mpdh.modifier,
        mpdh.modifier_id,
        mpdh.ctime,
        mpdh.mtime,
        mpdh.del_flag,
        mpdh.approval_status,
        mpdh.approval_reason,
        mpdh.normal_work_days,
        mpdh.rest_work_days,
        mpdh.holidays_work_days,
        mpdh.ompensatory_days
    </sql>
    <!--    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">-->
    <!--        &lt;!&ndash;@mbg.generated&ndash;&gt;-->
    <!--        delete-->
    <!--        from mhour_personnel_delivery_hour-->
    <!--        where id = #{id,jdbcType=BIGINT}-->
    <!--    </delete>-->

    <insert id="batchSave" parameterType="com.gok.pboot.pms.entity.PersonnelDeliveryHour">
        insert into mhour_personnel_delivery_hour (id, reuse_date, project_id,
        project_code, project_name, project_status,
        revenue_dept_id, user_real_name, work_code, cw_due_attendance, attendance_days,
        project_consumed, gross_pay, social_security,
        accumulation_fund, wage_cost, social_security_cost,
        accumulation_fund_cost, total, total_project_cost,
        paying_place, dept_id, dept_name, remark,
        executor_user_id, executor_user_real_name, creator,
        creator_id, modifier, modifier_id,
        ctime, mtime, del_flag,
        approval_reason,normal_work_days,rest_work_days,
        holidays_work_days,ompensatory_days)
        values
        <foreach collection="list" separator="," item="personnelDeliveryHour">
            (#{personnelDeliveryHour.id},
            #{personnelDeliveryHour.reuseDate},
            #{personnelDeliveryHour.projectId},
            #{personnelDeliveryHour.projectCode},
            #{personnelDeliveryHour.projectName},
            #{personnelDeliveryHour.projectStatus},
            #{personnelDeliveryHour.revenueDeptId},
            #{personnelDeliveryHour.userRealName},
            #{personnelDeliveryHour.workCode},
            #{personnelDeliveryHour.cwDueAttendance},
            #{personnelDeliveryHour.attendanceDays},
            #{personnelDeliveryHour.projectConsumed},
            #{personnelDeliveryHour.grossPay},
            #{personnelDeliveryHour.socialSecurity},
            #{personnelDeliveryHour.accumulationFund},
            #{personnelDeliveryHour.wageCost},
            #{personnelDeliveryHour.socialSecurityCost},
            #{personnelDeliveryHour.accumulationFundCost},
            #{personnelDeliveryHour.total},
            #{personnelDeliveryHour.totalProjectCost},
            #{personnelDeliveryHour.payingPlace},
            #{personnelDeliveryHour.deptId},
            #{personnelDeliveryHour.deptName},
            #{personnelDeliveryHour.remark},
            #{personnelDeliveryHour.executorUserId},
            #{personnelDeliveryHour.executorUserRealName},
            #{personnelDeliveryHour.creator},
            #{personnelDeliveryHour.creatorId},
            #{personnelDeliveryHour.modifier},
            #{personnelDeliveryHour.modifierId},
            #{personnelDeliveryHour.ctime},
            #{personnelDeliveryHour.mtime},
            #{personnelDeliveryHour.delFlag},
            #{personnelDeliveryHour.approvalReason},
            #{personnelDeliveryHour.normalWorkDays},
            #{personnelDeliveryHour.restWorkDays},
            #{personnelDeliveryHour.holidaysWorkDays},
            #{personnelDeliveryHour.ompensatoryDays})
        </foreach>
    </insert>
    <insert id="batchUpdate" parameterType="com.gok.pboot.pms.entity.PersonnelDeliveryHour">
        <foreach collection="list" item="personnelDeliveryHour" separator=";">
            update mhour_personnel_delivery_hour
            set reuse_date = #{personnelDeliveryHour.reuseDate},
            project_id = #{personnelDeliveryHour.projectId},
            project_code = #{personnelDeliveryHour.projectCode},
            project_name = #{personnelDeliveryHour.projectName},
            project_status = #{personnelDeliveryHour.projectStatus},
            revenue_dept_id = #{personnelDeliveryHour.revenueDeptId},
            user_real_name = #{personnelDeliveryHour.userRealName},
            work_code = #{personnelDeliveryHour.workCode},
            cw_due_attendance = #{personnelDeliveryHour.cwDueAttendance},
            attendance_days = #{personnelDeliveryHour.attendanceDays},
            project_consumed = #{personnelDeliveryHour.projectConsumed},
            gross_pay = #{personnelDeliveryHour.grossPay},
            social_security = #{personnelDeliveryHour.socialSecurity},
            accumulation_fund = #{personnelDeliveryHour.accumulationFund},
            wage_cost = #{personnelDeliveryHour.wageCost},
            social_security_cost = #{personnelDeliveryHour.socialSecurityCost},
            accumulation_fund_cost = #{personnelDeliveryHour.accumulationFundCost},
            total = #{personnelDeliveryHour.total},
            total_project_cost = #{personnelDeliveryHour.totalProjectCost},
            paying_place = #{personnelDeliveryHour.payingPlace},
            dept_id = #{personnelDeliveryHour.deptId},
            dept_name = #{personnelDeliveryHour.deptName},
            remark = #{personnelDeliveryHour.remark},
            executor_user_id = #{personnelDeliveryHour.executorUserId},
            executor_user_real_name = #{personnelDeliveryHour.executorUserRealName},
            creator = #{personnelDeliveryHour.creator},
            creator_id = #{personnelDeliveryHour.creatorId},
            modifier = #{personnelDeliveryHour.modifier},
            modifier_id = #{personnelDeliveryHour.modifierId},
            ctime = #{personnelDeliveryHour.ctime},
            mtime = #{personnelDeliveryHour.mtime},
            del_flag = #{personnelDeliveryHour.delFlag},
            approval_status = #{personnelDeliveryHour.approvalStatus},
            approval_reason = #{personnelDeliveryHour.approvalReason},
            normal_work_days = #{personnelDeliveryHour.normalWorkDays},
            rest_work_days = #{personnelDeliveryHour.restWorkDays},
            holidays_work_days = #{personnelDeliveryHour.holidaysWorkDays},
            ompensatory_days = #{personnelDeliveryHour.ompensatoryDays}
            where id = #{personnelDeliveryHour.id}
        </foreach>
    </insert>
    <!--  id更新  -->
    <update id="updateById">
        UPDATE mhour_personnel_delivery_hour
        SET project_consumed = #{personnelReuseUpdateDTO.aggregatedDays},
            remark           = #{personnelReuseUpdateDTO.remark},
            modifier         = #{personnelReuseUpdateDTO.modifier},
            modifier_id      = #{personnelReuseUpdateDTO.modifierId},
            mtime            = #{personnelReuseUpdateDTO.mtime},
            approval_status  = #{personnelReuseUpdateDTO.approvalStatus}
        WHERE id = #{personnelReuseUpdateDTO.id}
    </update>
    <!--  批量逻辑删除  -->
    <update id="batchDel">
        update mhour_personnel_delivery_hour set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateApprovalStatusById">
        UPDATE mhour_personnel_delivery_hour
        SET
        approval_status = #{param.approvalStatus},
        modifier = #{param.approvalName},
        <if test="param.approvalReason!= null and param.approvalReason != '' ">
            approval_reason = #{param.approvalReason},
        </if>
        modifier_id = #{param.approvalId},
        mtime = #{param.mtime}
        WHERE
        id = #{param.id}
    </update>
    <update id="updateApprovalStatusByIds">
        UPDATE mhour_personnel_delivery_hour
        SET
        approval_status = 4,
        modifier = #{auditName},
        modifier_id = #{auditId},
        mtime = #{mTime}
        WHERE
        id in
        <foreach collection="reuseIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!--  按日期查找交付人员工时条目  -->
    <select id="selectListByReuseDate" resultType="com.gok.pboot.pms.entity.PersonnelDeliveryHour">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_personnel_delivery_hour mpdh
        WHERE mpdh.del_flag = 0
        AND mpdh.reuse_date = #{yearMonth}
    </select>
    <select id="findList" resultType="com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO">
        SELECT
        <include refid="Base_Column_List"/>,
        b.manager_user_name as managerUserName,
        b.project_salesperson as projectSalesperson
        FROM
        mhour_personnel_delivery_hour mpdh
        left join project_info b on mpdh.project_id = b.id
        <where>
            mpdh.del_flag = 0
            <if test="filter.projectId !=null">
                AND mpdh.project_id = #{filter.projectId}
            </if>
            <if test="filter.projectName !=null and filter.projectName != '' ">
                AND (mpdh.project_name like concat('%',#{filter.projectName},'%')
                OR mpdh.project_code like concat('%',#{filter.projectName},'%'))
            </if>
            <if test="filter.time != null">
                AND mpdh.reuse_date =#{filter.time}
            </if>
            <if test="filter.status != null">
                AND mpdh.approval_status = #{filter.status}
            </if>
            <if test="filter.name != null and filter.name != ''">
                AND (mpdh.user_real_name like concat('%',#{filter.name},'%')
                OR mpdh.executor_user_real_name like concat('%',#{filter.name},'%')
                OR b.manager_user_name like concat('%',#{filter.name},'%')
                OR b.project_salesperson like concat('%',#{filter.name},'%'))
            </if>
            <if test="filter.userId != null ">
                AND mpdh.executor_user_id = #{filter.userId}
            </if>
        </where>
        ORDER BY FIELD(mpdh.approval_status,3,2,4) asc, mpdh.ctime desc, mpdh.id asc
    </select>

    <select id="selectDailyPaperById" resultType="com.gok.pboot.pms.entity.dto.DailyPaperCommonDTO">
        select id as dailyPaperId, project_id, project_name, reuse_date as submissionDate, executor_user_id as userId
        from mhour_personnel_delivery_hour
        where del_flag = 0
          and id = #{id}
    </select>
    <select id="getAlreadyImportProjectIds" resultType="java.lang.Long" parameterType="java.time.LocalDate">
        select distinct mpdh.project_id
        from mhour_personnel_delivery_hour as mpdh
        join project_info as pi on mpdh.project_id = pi.id
        where mpdh.del_flag = 0
        and mpdh.project_status = ${@<EMAIL>()}
        and pi.secondary_business_type =
        ${@com.gok.pboot.pms.enumeration.SecondaryBusinessTypeEnum@DIGITAL_TALENT_SERVICE.getValue()}
        <if test="date != null">
            and mpdh.reuse_date = #{date}
        </if>
    </select>

    <select id="findByWorkCodeAndReuseDate"
            resultType="com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO">
        SELECT
            *
        FROM
            mhour_personnel_delivery_hour
        WHERE
            del_flag = 0
        <if test="reuseDateList != null and reuseDateList.size > 0">
            AND reuse_date IN
            <foreach item="reuseDate" collection="reuseDateList" index="index"
                     open="(" close=")" separator=",">
                #{reuseDate}
            </foreach>
        </if>
        <if test="workCodeList != null and workCodeList.size > 0">
            AND work_code IN
            <foreach item="workCode" collection="workCodeList" index="index"
                     open="(" close=")" separator=",">
                #{workCode}
            </foreach>
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            AND project_id IN
            <foreach item="projectId" collection="projectIds" index="index"
                     open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>


</mapper>