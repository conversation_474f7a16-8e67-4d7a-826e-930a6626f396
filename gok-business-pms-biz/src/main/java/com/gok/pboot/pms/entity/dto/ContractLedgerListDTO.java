package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 合同台账dto
 *
 * <AUTHOR>
 * @date 2024/02/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractLedgerListDTO extends PageRequest {

    /**
     * ids
     */
    private List<Long> ids;

    /**
     * 合同编号/名称
     */
    private String htmc;

    /**
     * 合同细类集合
     */
    private List<Integer> htxlIds;

    /**
     * 合同细类
     */
    private Integer htxl;
    /**
     * 合同细类-统计
     */
    private Integer htxlSum;

    /**
     * 合同所属公司
     */
    private Integer htssgs;

    /**
     * 业务板块
     */
    private Integer ywbk;

    /**
     * 项目编号/名称
     */
    private String xmmc;

    /**
     * 对方名称(字段【客户名称】【供应商名称】模糊搜索)
     */
    private String khmc;

    /**
     * 技术类型
     */
    private Integer jslx;

    /**
     * 收入类型
     */
    private Integer srlx;

    /**
     * 客户/项目经理(【客户经理】【项目经理】模糊搜索)
     */
    private String khjl;

    /**
     * 合同起始日期
     */
    private LocalDate htqsrq;

    /**
     * 合同截止日期
     */
    private LocalDate htjzrq;

    /**
     * 合同状态
     */
    private Integer htzt;

    /**
     * 业务归属部门ids
     */
    private List<Long> deptIds;


    /**
     * 是否导出
     */
    private Boolean ifExport=false;


}
