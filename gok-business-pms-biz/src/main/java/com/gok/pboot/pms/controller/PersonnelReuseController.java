package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.excel.annotation.RequestExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.PersonnelReuse;
import com.gok.pboot.pms.entity.dto.PersonnelReuseImproExcelDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelReusePageVO;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import com.gok.pboot.pms.handler.PersonnelReuseAnalysisEventListener;
import com.gok.pboot.pms.service.IPersonnelReuseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
/**
 *
 * @description 人才复用 前端控制器
 * <AUTHOR>
 * @since 2022-09-01
 * @menu 人才复用
 */
@Slf4j
@RestController
@RequestMapping("personnelReuse")
public class PersonnelReuseController extends BaseController {

    private IPersonnelReuseService service;

    @Autowired
    public PersonnelReuseController(IPersonnelReuseService service) {
        this.service = service;
    }

    /**
     * 导入人才复用
     * @param excelVOList 列表
     * @param bindingResult 错误信息列表
     * @return R
     */
    @PostMapping("/import")
    public ApiResult importUser(@RequestExcel(readListener = PersonnelReuseAnalysisEventListener.class,ignoreEmptyRow = true)
                                            List<PersonnelReuseImproExcelDTO> excelVOList, BindingResult bindingResult,
                                @RequestParam("file") MultipartFile file) {
        ApiResult apiResult = null;
        try {
            apiResult = service.importUser(excelVOList, bindingResult,file);
        }catch (Exception e){
            log.warn("人才复用导入解析错误", e);
            return ApiResult.builder().apiResultEnum(ApiResultEnum.IMPORT_VAILD_FAIL).result("导入解析错误,请联系管理员！").build();
        }
        return apiResult;
    }

    /**
     * 分页查询人才复用
     *
     * @param pageRequest
     * @param request
     *@customParam filter_S_projectName 项目名称
     *@customParam filter_S_reuseDate 月份时间
     * @return {@link ApiResult<Page<PersonnelReuse>>}
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_MULTIPLEXING')")
    @GetMapping("/findPage")
    public ApiResult<Page<PersonnelReusePageVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return success(service.findPage(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 编辑
     * @param personnelReuseUpdateDTO
     * @return {@link ApiResult}
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody @Valid PersonnelReuseUpdateDTO personnelReuseUpdateDTO) {
        return service.update(personnelReuseUpdateDTO);
    }


    /**
     * 批量逻辑删除
     * @param list 要删除的id集合[]
     * @return {@link ApiResult}
     */
    @PostMapping("/batchDel")
    public ApiResult<String> batchDel(@RequestBody List<Long> list) {
        return service.batchDel(list);
    }
}
