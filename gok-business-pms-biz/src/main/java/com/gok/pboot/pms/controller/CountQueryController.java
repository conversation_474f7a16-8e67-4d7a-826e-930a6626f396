package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.DailyPaperAbnormalEnum;
import com.gok.pboot.pms.service.ICountQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * 统计查询前端控制器
 *
 * <AUTHOR>
 * @description 统计查询 前端控制器
 * @menu 日报提交一览表
 * @since 2022-08-23
 */
@Slf4j
@RestController
@RequestMapping("/countQuery")
@RequiredArgsConstructor
public class CountQueryController extends BaseController {
    private final ICountQueryService service;

    /**
     * 查询异常日报统计信息（日报提交一览表）
     *
     * @param dailyFindPageDTO 查询参数
     * @return 统计数据
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/findAbnormalStatistics")
    public ApiResult<AbnormalStatisticsVO> dailyFindAbnormalStatistics(DailyFindPageDTO dailyFindPageDTO) {
        return ApiResult.success(service.findAbnormalStatistics(dailyFindPageDTO));
    }

    /**
     * 分页 查询日报提交一览表
     *
     * @param pageRequest      分页请求对象
     * @param dailyFindPageDTO 日报查找分页对象
     * @return {@link ApiResult<Page<DailyFindPageVO>>}
     * @customParam name 姓名
     * @customParam deptId 部门编号
     * @customParam personnelStatus 人员状态
     * @customParam startTime 日期开始时间
     * @customParam endTime 日期结束时间
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/findPage")
    public ApiResult<Page<DailyFindPageVO>> dailyFindPage(PageRequest pageRequest, DailyFindPageDTO dailyFindPageDTO) {
        return service.dailyFindPage(pageRequest, dailyFindPageDTO);
    }

    /**
     * 分页 查询日报提交一览表导出
     *
     * @param dailyFindPageDTO 日报查找分页对象
     * @return {@link ApiResult<Page<DailyFindPageVO>>}
     * @customParam name 姓名
     * @customParam deptId 部门编号
     * @customParam personnelStatus 人员状态
     * @customParam startTime 日期开始时间
     * @customParam endTime 日期结束时间
     */
    @ResponseExcel(name = "日报提交一览表导出")
    @GetMapping("/daily/exportExcel")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    public List<DailyExcelExportVO> dailyExport(DailyFindPageDTO dailyFindPageDTO) {
        return service.dailyExport(dailyFindPageDTO);
    }

    /**
     * ~ 查询异常日报 ~
     *
     * @param pageRequest      分页对象
     * @param dailyFindPageDTO 查询参数
     * @return com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.gok.pboot.pms.entity.vo.AbnormalDailyPaperVO>>
     * <AUTHOR>
     * @date 2022/11/11 17:48
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/findAbnormalPage")
    public ApiResult<AbnormalDailyPaperFindPageVO> dailyFindAbnormalPage(PageRequest pageRequest, DailyFindPageDTO dailyFindPageDTO) {
        return service.findAbnormalPage(pageRequest, dailyFindPageDTO, true);
    }

    /**
     * ~ 对异常日报的人员 发送 企业微信消息提醒（手动触发） ~
     *
     * @param dailyFindPageDTO 查询参数
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/sendWeiXinCpMsgForAbnormal")
    public ApiResult<Void> sendWeiXinCpMsgForAbnormal(DailyFindPageDTO dailyFindPageDTO) {
        PigxUser pigxUser = SecurityUtils.getUser();
        List<AbnormalDailyPaperExcelVO> list = service.findAbnormal(dailyFindPageDTO, DailyPaperAbnormalEnum.SUBMIT.getValue());
        service.sendWeiXinCpMessageForAbnormal(dailyFindPageDTO, list, pigxUser);

        return ApiResult.success(null);
    }

    /**
     * 定时器
     * 对异常日报的人员发送企业微信 消息提醒 ~ 远程调用过滤  0 30 10 14,27 * ? *
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     */
    @Inner(value = false)
    @GetMapping("/daily/sendWeiXinCpMsgForAbnormalByFilter")
    public ApiResult<Void> sendWeiXinCpMsgForAbnormalByFilter() {
        log.info("定时任务触发, 每月14/27推送提交异常日报执行开始");
        DailyFindPageDTO dto = new DailyFindPageDTO();
        dto.setStartTime(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()));
        dto.setEndTime(LocalDate.now());
        List<AbnormalDailyPaperExcelVO> list = service.findAbnormal(dto, DailyPaperAbnormalEnum.SUBMIT.getValue());
        service.sendWeiXinCpMessageForAbnormal(dto, list, null);
        log.info("定时任务触发, 每月14/27推送提交异常日报执行结束");
        return ApiResult.success(null);
    }

    /**
     * ~ 对异常日报的审核人员发送企业微信消息提醒（手动触发）  ~
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/sendWeiXinCpMsgForAuditor")
    public ApiResult<Void> sendWeiXinCpMsgForAuditor(DailyFindPageDTO dailyFindPageDTO) {
        PigxUser pigxUser = SecurityUtils.getUser();
        service.sendWeiXinCpMsgForAuditor(dailyFindPageDTO, true, pigxUser);

        return ApiResult.success(null);
    }

    /**
     * 定时器
     * ~ 对异常日报的审核人员发送企业微信消息提醒 ~   0 30 10 14,27 * ? *
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     */
    @Inner(value = false)
    @GetMapping("/daily/sendWeiXinCpMsgForAuditorByFilter")
    public ApiResult<Void> sendWeiXinCpMsgForAuditorByFilter() {
        log.info("定时任务触发, 每月14/27推送未审核日报执行开始");
        DailyFindPageDTO dto = new DailyFindPageDTO();

        dto.setStartTime(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()));
        dto.setEndTime(LocalDate.now());
        service.sendWeiXinCpMsgForAuditor(dto, false, null);
        log.info("定时任务触发, 每月14/27推送未审核日报执行结束");
        return ApiResult.success(null);
    }

    /**
     * 导出异常日报
     *
     * @param dailyFindPageDTO 日报查找分页对象
     * @return {@link List}<{@link com.gok.pboot.pms.entity.vo.AbnormalDailyPaperExcelVO}>
     */
    @ResponseExcel(name = "异常日报")
    @GetMapping("/daily/exportAbnormal")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    public List<AbnormalDailyPaperExcelVO> exportAbnormal(DailyFindPageDTO dailyFindPageDTO) {
        return service.findAbnormal(dailyFindPageDTO);
    }

    /**
     * 导出未审核日报条目
     *
     * @param projectHourSumFindPageDTO PMS项目工时汇总
     * @return {@link List}<{@link com.gok.pboot.pms.entity.vo.UnReviewedDailyPaperEntryExcelVO}>
     * @throws ClassNotFoundException 类未找到异常
     * @throws IllegalAccessException 非法进入异常
     * @throws InstantiationException 实例化异常
     */
    @ResponseExcel(name = "未审核日报条目")
    @GetMapping("/daily/exportUnreviewed2")
    public List<UnReviewedDailyPaperEntryExcelVO> exportUnreviewed(
            @Valid ProjectHourSumFindPageDTO projectHourSumFindPageDTO
    ) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        return service.exportUnreviewed(projectHourSumFindPageDTO);
    }

    /**
     * 项目工时汇总的日报条目导出
     * 包含【全部、已审核、未审核】 多个sheet页数据
     *
     * @param projectHourSumFindPageDTO 查询参数
     * @param response                  httpservlet响应体
     */
    @GetMapping("/daily/exportUnreviewed")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_SUMMARY')")
    public void exportReviewedAndUnreviewed(HttpServletResponse response,
                                            @Valid ProjectHourSumFindPageDTO projectHourSumFindPageDTO) {
        service.exportReviewedAndUnreviewed(response, projectHourSumFindPageDTO);
    }

    /**
     * 获取工时分摊表概览
     *
     * @return {@link ApiResult}<{@link AllocationOverviewVO}>
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_ALLICATION')")
    @PostMapping("/allocation/overview")
    public ApiResult<AllocationOverviewVO> findOverview(@RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        return ApiResult.success(service.findOverview(allocationFindPageDTO));
    }

    /**
     * 分页查询 项目工时分摊表
     *
     * @param allocationFindPageDTO PMS项目工时分摊表
     * @return {@link ApiResult<Page<AllocationFindPageVO>>}
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_ALLICATION')")
    @PostMapping("/allocation/findPage")
    public ApiResult<Page<AllocationFindPageVO>> allocationFindPage(@RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        return service.allocationFindPage(false, allocationFindPageDTO);
    }


    /**
     * 项目工时分摊表导出
     *
     * @param response              httpservlet响应体
     * @param allocationFindPageDTO PMS项目工时分摊表
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_ALLICATION')")
    @PostMapping("/allocation/export")
    public void allocationExport(HttpServletResponse response, @RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        service.allocationExport(response, allocationFindPageDTO);
    }


    /**
     * 获取工时分摊表概览
     * 项目详情-交付管理-考勤工时
     *
     * @return {@link ApiResult}<{@link AllocationOverviewVO}>
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PostMapping("/project/allocation/overview")
    public ApiResult<AllocationOverviewVO> projectFindOverview(@RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        return ApiResult.success(service.findOverview(allocationFindPageDTO));
    }

    /**
     * 分页查询 项目工时分摊表
     * 项目详情-交付管理-考勤工时
     *
     * @param allocationFindPageDTO PMS项目工时分摊表
     * @return {@link ApiResult<Page<AllocationFindPageVO>>}
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PostMapping("/project/allocation/findPage")
    public ApiResult<Page<AllocationFindPageVO>> projectAllocationFindPage(@RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        return service.allocationFindPage(false, allocationFindPageDTO);
    }


    /**
     * 项目工时分摊表导出
     * 项目详情-交付管理-考勤工时
     *
     * @param response              httpservlet响应体
     * @param allocationFindPageDTO PMS项目工时分摊表
     * @customParam userIds 用户ID集合
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PostMapping("/project/allocation/export")
    public void projectAllocationExport(HttpServletResponse response, @RequestBody @Valid AllocationFindPageDTO allocationFindPageDTO) {
        service.allocationExport(response, allocationFindPageDTO);
    }

    /**
     * 分页查询项目工时汇总
     *
     * @param pageRequest               分页请求对象
     * @param projectHourSumFindPageDTO PMS项目工时汇总
     * @param showUnreviewed            显示未审核（如果为null，展示所有，如果为false，展示已审核）
     * @return {@link ApiResult}<{@link Page}<{@link ProjectHourSumFindPageVO}>>
     * @throws ClassNotFoundException 类未找到异常
     * @throws IllegalAccessException 非法访问异常
     * @throws InstantiationException 实例化异常
     * @customParam projectId 所属项目ID
     * @customParam startTime 日期开始时间
     * @customParam endTime 日期结束时间
     * @customParam projectStatus 项目状态
     * @customParam personnelStatus 人员状态
     */
    @GetMapping("/projectHourSum/findPage")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_SUMMARY')")
    public ApiResult<Page<ProjectHourSumFindPageVO>> projectHourSumFindPage(
            PageRequest pageRequest,
            @Valid ProjectHourSumFindPageDTO projectHourSumFindPageDTO,
            @Nullable @RequestParam(value = "showUnreviewed", required = false) Boolean showUnreviewed
    ) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        return service.projectHourSumFindPageV2(pageRequest, projectHourSumFindPageDTO, showUnreviewed);
    }

    /**
     * 项目工时汇总导出
     *
     * @param projectHourSumFindPageDTO PMS项目工时汇总
     * @param showUnreviewed            是否展示未审核
     * @return {@link List}<{@link ProjectHourSumFindPageVO}>
     * @throws ClassNotFoundException 类未找到异常
     * @throws IllegalAccessException 非法访问异常
     * @throws InstantiationException 实例化异常
     */
    @ResponseExcel(name = "项目工时汇总导出")
    @GetMapping("/projectHourSum/export2")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_SUMMARY')")
    public List<ProjectHourSumFindPageVO> projectHourSumExport2(
            @Valid ProjectHourSumFindPageDTO projectHourSumFindPageDTO,
            @Nullable @RequestParam(value = "showUnreviewed", required = false) Boolean showUnreviewed
    ) throws ClassNotFoundException, IllegalAccessException, InstantiationException {

        return service.projectHourSumExport2(projectHourSumFindPageDTO, showUnreviewed);
    }

    /**
     * 项目工时汇总导出
     *
     * @param response                  httpservlet响应体
     * @param projectHourSumFindPageDTO PMS项目工时汇总
     * @customParam projectId 所属项目ID
     * @customParam startTime 日期开始时间
     * @customParam endTime 日期结束时间
     * @customParam projectStatus 项目状态
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_SUMMARY')")
    @GetMapping("/projectHourSum/export")
    public void projectHourSumExport(HttpServletResponse response, @Valid ProjectHourSumFindPageDTO projectHourSumFindPageDTO) {
        service.projectHourSumExport(response, projectHourSumFindPageDTO);
    }

    /**
     * 项目工时明细分页查询
     *
     * @param dto 查询条件
     * @return {@link ApiResult<Page<ProjectHourDetailsFindPageVO>>}
     */
    @GetMapping("/projectHourDetails/findPage")
    public ApiResult<Page<ProjectHourDetailsFindPageVO>> projectHourDetailsFindPage(ProjectHourDetailsFindPageDTO dto) {
        return service.projectHourDetailsFindPage(dto);
    }

    /**
     * 项目工时明细导出
     *
     * @param dto 查询条件
     * @return {@link ApiResult<Page<ProjectHourDetailsFindPageVO>>}
     */
    @GetMapping("/projectHourDetails/export")
    @ResponseExcel(name = "项目工时明细.xlsx")
    public List<ProjectHourDetailsFindPageVO> projectHourDetailsExport(ProjectHourDetailsFindPageDTO dto) {
        return service.projectHourDetailsExport(dto);
    }

    /**
     * 交付人员表-分页查询
     *
     * @param dto 查询条件
     * @return {@link ApiResult<Page<DelivererFindPageVO>>}
     */
    @GetMapping("/deliverer/findPage")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DELIVERY_PERSONNEL')")
    public ApiResult<Page<DelivererFindPageVO>> delivererFindPage(DelivererFindPageDTO dto) {
        return service.delivererFindPage(dto);
    }

    /**
     * 导出 交付人员
     *
     * @param dto 查询条件
     * @return {@link ApiResult<Page<DelivererFindPageVO>>}
     */
    @GetMapping("/deliverer/export")
    @ResponseExcel(name = "交付人员工时报表", timePrefix = "yyyy-MM")
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DELIVERY_PERSONNEL')")
    public List<DelivererFindPageVO> delivererExport(DelivererFindPageDTO dto) {
        return service.delivererExport(dto);
    }

    /**
     * 分页 查询人才复用
     *
     * @param pageRequest               分页请求对象
     * @param personnelReuseFindPageDTO PMS人才复用表
     * @return {@link ApiResult<Page< PersonnelReuseFindPageVO >>}
     * @customParam exportName 导入人员姓名
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_MULTIPLEXING')")
    @GetMapping("/personnelReuse/findPage")
    public ApiResult<Page<PersonnelReuseFindPageVO>> personnelReuseFindPage(PageRequest pageRequest, @Valid PersonnelReuseFindPageDTO personnelReuseFindPageDTO) {
        return service.personnelReuseFindPage(pageRequest, personnelReuseFindPageDTO);
    }

    /**
     * 导出 -人才复用
     *
     * @param response                  httpservlet响应体
     * @param personnelReuseFindPageDTO PMS人才复用表
     * @return {@link ApiResult<Page< ProjectVO >>}
     * @customParam exportName 导入人员姓名
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_MULTIPLEXING')")
    @GetMapping("/personnelReuse/export")
    public void personnelReuseExport(HttpServletResponse response, @Valid PersonnelReuseFindPageDTO personnelReuseFindPageDTO) {
        service.personnelReuseExport(response, personnelReuseFindPageDTO);
    }

    /**
     * ~ 工时明细表- 分页查询 ~
     *
     * @param dto 分页对象
     *            oaIds         执行人ID列表
     *            projectIds    项目ID列表
     *            startDate     开始时间
     *            endDate       结束时间
     *            taskId       任务id
     * @return com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.gok.pboot.pms.entity.vo.DailyPaperEntryVO>>
     * <AUTHOR>
     * @date 2022/10/17 16:34
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_TASK_TIME')")
    @PostMapping("/workHourStatistics/findPage")
    public ApiResult<WorkHourStatisticsFindPageVO> workHourStatistics(@RequestBody WorkHourStatisticsFindPageDTO dto) {
        return ApiResult.success(service.workHourStatisticsFindPage(dto));
    }

    /**
     * 导出 -日报提交一览表
     *
     * @param dailyFindPageDTO 查询请求
     */
    @Deprecated
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_DAILY_CONFIRM')")
    @GetMapping("/daily/export")
    public void exportDaily(DailyFindPageDTO dailyFindPageDTO) {
        log.info("日报提交一览表方法已废弃，参数: {}", dailyFindPageDTO);
    }
}
