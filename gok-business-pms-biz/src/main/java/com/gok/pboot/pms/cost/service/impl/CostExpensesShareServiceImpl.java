package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostExpensesShare;
import com.gok.pboot.pms.cost.entity.dto.AutoBringConditionDTO;
import com.gok.pboot.pms.cost.entity.dto.CostExpensesShareListDto;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.CustomerUndertakesEnum;
import com.gok.pboot.pms.cost.mapper.CostExpensesShareMapper;
import com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper;
import com.gok.pboot.pms.cost.service.ICostExpensesShareService;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交付管理费用分摊服务实现
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class CostExpensesShareServiceImpl extends ServiceImpl<CostExpensesShareMapper, CostExpensesShare> implements ICostExpensesShareService {

    private final CostExpensesShareMapper costExpensesShareMapper;
    private final CostPersonnelInformationMapper costPersonnelInformationMapper;
    private final ProjectScopeHandle projectScopeHandle;
    private static final String CONTENT_TYPE_SHEET = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";
    private static final String MENU_CODE = "DELIVERY_HUMAN_RESOURCE_FYFT";

    @Override
    public CostExpensesShareListVO findExpensesShare(CostExpensesShareListDto dto) {
        CostExpensesShareListVO vo = new CostExpensesShareListVO();
        SysUserDataScopeVO dataPermission = projectScopeHandle.getDeliverManagementDataPermission(MENU_CODE, dto.getProjectId(), null);
        if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
            dto.setPurviewNames(dataPermission.getUserNameList());
        }

        //查询人员信息
        AutoBringConditionDTO autoBringConditionDTO = new AutoBringConditionDTO();
        autoBringConditionDTO.setProjectId(dto.getProjectId());
        List<CostPersonnelInformationVO> personAutoBringInfo = costPersonnelInformationMapper.getPersonAutoBringInfo(autoBringConditionDTO);
        Map<String, List<CostPersonnelInformationVO>> personMap = personAutoBringInfo.stream().collect(Collectors.groupingBy(CostPersonnelInformationVO::getName));
        Set<String> nameSet = personAutoBringInfo.stream().map(CostPersonnelInformationVO::getName).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(nameSet)) {
            vo.setCostExpensesShareSumDataVO(CostExpensesShareSumDataVO.builder()
                    .expensesTotal(DecimalFormatUtil.ZERO)
                    .gokBearsCost(DecimalFormatUtil.ZERO)
                    .customerBearsCost(DecimalFormatUtil.ZERO).build());
            vo.setSummaryList(ListUtil.empty());
            vo.setDetailsList(ListUtil.empty());
            return vo;
        }
        dto.setNameSet(nameSet);
        CostExpensesShareSumDataVO costExpensesShareSumDataVO = costExpensesShareMapper.selSummary(dto);
        vo.setCostExpensesShareSumDataVO(costExpensesShareSumDataVO);
        List<CostExpensesShareSummaryListVO> summaryList = costExpensesShareMapper.selSummaryList(dto);
        vo.setSummaryList(summaryList);
        List<CostExpensesShareDetailsVO> detailsVOList = costExpensesShareMapper.selDetailsList(dto);
        List<CostExpensesShareDetailsListVO> detailsList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(detailsVOList)) {
            List<CostExpensesShareDetailsVO> filterDetailsVOList = detailsVOList.stream().filter(d -> Optional.ofNullable(d.getAccountCategoryId()).isPresent()).collect(Collectors.toList());
            filterDetailsVOList.forEach(d -> {
                d.setCustomerUndertakesTxt(EnumUtils.getNameByValue(CustomerUndertakesEnum.class, d.getCustomerUndertakes()));
                List<CostPersonnelInformationVO> personnelList = personMap.getOrDefault(d.getRecipientUserName(), ListUtil.empty());
                if (CollectionUtil.isNotEmpty(personnelList) && NumberUtils.INTEGER_ONE.equals(personnelList.size())) {
                    d.setWorkCode(personnelList.get(NumberUtils.INTEGER_ZERO).getWorkCode());
                    d.setDepartmentId(personnelList.get(NumberUtils.INTEGER_ZERO).getDeptId());
                    d.setDepartmentName(personnelList.get(NumberUtils.INTEGER_ZERO).getDeptName());
                }
            });
            Map<Long, List<CostExpensesShareDetailsVO>> detailsMap = filterDetailsVOList.stream().collect(Collectors.groupingBy(CostExpensesShareDetailsVO::getAccountCategoryId));
            detailsList = detailsMap.keySet().stream().map(e -> {
                List<CostExpensesShareDetailsVO> detailsVOs = detailsMap.get(e);
                return CostExpensesShareDetailsListVO.builder()
                        .accountCategoryId(e)
                        .accountCategoryName(detailsVOs.get(NumberUtils.INTEGER_ZERO).getAccountCategoryName())
                        .detailsVOList(detailsVOs)
                        .build();
            }).collect(Collectors.toList());

        }
        vo.setDetailsList(detailsList);
        return vo;
    }

    @Override
    public void exportExpensesShare(HttpServletResponse response, CostExpensesShareListDto dto) {
        try {
            CostExpensesShareListDto dto1 =
                    BeanUtil.copyProperties(dto, CostExpensesShareListDto.class);
            dto1.setCustomerUndertakes(NumberUtils.INTEGER_ZERO);
            CostExpensesShareListDto dto2 =
                    BeanUtil.copyProperties(dto, CostExpensesShareListDto.class);
            dto2.setCustomerUndertakes(NumberUtils.INTEGER_ONE);
            CostExpensesShareListVO expensesShare1 = findExpensesShare(dto1);
            CostExpensesShareListVO expensesShare2 = findExpensesShare(dto2);

            // 文件名
            String fileName = URLEncoder.encode(
                    dto.getBelongingStartMonth()
                            + "-"
                            + dto.getBelongingEndMonth()
                            + "费用分摊",
                    StandardCharsets.UTF_8.name()
            ).replaceAll("\\+", "%20");

            // 写入响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "客户承担").build();
            exportMake(expensesShare1, excelWriter, sheet1);
            WriteSheet sheet2 = EasyExcel.writerSheet(1, "国科承担").build();
            exportMake(expensesShare2, excelWriter, sheet2);

            // 关闭excelWriter
            excelWriter.finish();
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出sheet组装多表格
     *
     * @param expensesShare
     * @param excelWriter
     * @param sheet
     */
    public void exportMake(CostExpensesShareListVO expensesShare, ExcelWriter excelWriter, WriteSheet sheet) {
        //导出汇总数据
        CostExpensesShareSumDataVO sumDataVO = expensesShare.getCostExpensesShareSumDataVO();
        List<CostExpensesShareSumDataVO> sumDataList = new ArrayList<>();
        sumDataList.add(sumDataVO);
        WriteTable writeTable1 = EasyExcel.writerTable(NumberUtils.INTEGER_ZERO).needHead(Boolean.TRUE).head(CostExpensesShareSumDataVO.class).build();
        excelWriter.write(sumDataList, sheet, writeTable1);
        //导出汇总列表
        WriteTable writeTable2 = EasyExcel.writerTable(NumberUtils.INTEGER_ONE).needHead(Boolean.TRUE).head(CostExpensesShareSummaryListVO.class).build();
        excelWriter.write(expensesShare.getSummaryList(), sheet, writeTable2);
        //导出明细列表
        List<CostExpensesShareDetailsListVO> detailsList = expensesShare.getDetailsList();
        if (CollectionUtil.isNotEmpty(detailsList)) {
            int num = NumberUtils.INTEGER_TWO;
            for (CostExpensesShareDetailsListVO detailsListVO : expensesShare.getDetailsList()) {
                List<List<String>> nameHeader = new ArrayList<>();
                nameHeader.add(Collections.singletonList(detailsListVO.getAccountCategoryName()));
                WriteTable writeTable3 = EasyExcel.writerTable(num).needHead(Boolean.TRUE).head(nameHeader).build();
                excelWriter.write(null, sheet, writeTable3);
                WriteTable writeTable4 = EasyExcel.writerTable(num + 1).needHead(Boolean.TRUE).head(CostExpensesShareDetailsVO.class).build();
                excelWriter.write(detailsListVO.getDetailsVOList(), sheet, writeTable4);
                num = num + 2;
            }
        }
    }
}
