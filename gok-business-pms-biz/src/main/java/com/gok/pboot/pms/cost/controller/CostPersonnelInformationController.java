package com.gok.pboot.pms.cost.controller;

import com.gok.components.excel.annotation.RequestExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.entity.vo.PersonPartInfoVO;
import com.gok.pboot.pms.cost.service.ICostPersonnelInformationService;
import com.gok.pboot.pms.handler.PersonImportInfoEventListener;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 人员信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 人员信息
 * @since 2025-02-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/costPersonnelInformation")
public class CostPersonnelInformationController {

    private final ICostPersonnelInformationService costPersonnelInformationService;

    /**
     * 保存或更新人员信息
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link List}<{@link String}>>
     */
    @PostMapping("/saveOrUpdatePersonInfo")
    public ApiResult<List<String>> saveOrUpdatePersonInfo(@RequestBody CostPersonnelInformationDTO dto) {
        return ApiResult.success(costPersonnelInformationService.saveOrUpdatePersonInfo(dto));
    }

    /**
     * 根据条件获取在场/已离场人员信息
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link List}<{@link CostPersonnelInformationVO}>>
     */
    @PostMapping("/getPersonInfoList")
    public ApiResult<List<CostPersonnelInformationVO>> getPersonInfoList(@RequestBody PersonInfoConditionDTO dto) {
        return ApiResult.success(costPersonnelInformationService.getPersonInfoList(dto));
    }

    /**
     * 根据条件获取含税报价信息
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link BigDecimal}>
     */
    @PostMapping("/getQuotationIncludeTax")
    public ApiResult<BigDecimal> getQuotationIncludeTax(@RequestBody AutoBringConditionDTO dto) {
        return ApiResult.success(costPersonnelInformationService.getQuotationIncludeTax(dto));
    }

    /**
     * 根据条件自动带出人员信息列表
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link List}<{@link CostPersonnelInformationVO}>>
     */
    @PostMapping("/getPersonAutoBringInfoList")
    public ApiResult<List<CostPersonnelInformationVO>> getPersonAutoBringInfoList(@RequestBody AutoBringConditionDTO dto) {
        return ApiResult.success(costPersonnelInformationService.getPersonAutoBringInfo(dto));
    }

    /**
     * 根据条件获取新增国科人员信息
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link List}<{@link PersonPartInfoVO}>>
     */
    @PostMapping("/getPersonPartInfo")
    public ApiResult<List<PersonPartInfoVO>> getPersonPartInfo(@RequestBody PersonPartInfoDTO dto) {
        return ApiResult.success(costPersonnelInformationService.getPersonPartInfo(dto));
    }

    /**
     * 离场/再次入场
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link String}>
     */
    @PutMapping("/exitOrEntry")
    public ApiResult<Boolean> exitOrEntry(@RequestBody ExitOrDeleteOrEntryPersonDTO dto) {
        return ApiResult.success(costPersonnelInformationService.exitOrEntry(dto));
    }

    /**
     * 删除
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link String}>
     */
    @DeleteMapping("/deletePersonInfo")
    public ApiResult<String> deletePersonInfo(@RequestBody ExitOrDeleteOrEntryPersonDTO dto) {
        return costPersonnelInformationService.deletePersonInfo(dto) ? ApiResult.success("删除成功") : ApiResult.failure("删除失败");
    }

    /**
     * 导出
     *
     * @param response HTTP
     * @param dto      dto实体
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody PersonInfoConditionDTO dto) {
        costPersonnelInformationService.export(response, dto);
    }

    /**
     * 导入
     *
     * @param projectId 项目ID
     * @param dtoList   dto列表
     * @return {@link ApiResult}<{@link List}<{@link String}>>
     */
    @PostMapping("/importPersonData")
    public ApiResult<List<String>> importPersonData(@RequestParam("projectId") Long projectId, @RequestExcel(readListener = PersonImportInfoEventListener.class) List<PersonImportInfoDTO> dtoList) {
        return ApiResult.success(costPersonnelInformationService.importPersonData(projectId, dtoList));
    }

    /**
     * 每月一号定时归档人员信息
     *
     * @return {@link String}
     */
    @Inner(value = false)
    @GetMapping("/filingPersonInfo")
    public ApiResult<String> filingPersonInfo() {
        return costPersonnelInformationService.filingPersonInfo() ? ApiResult.success("归档成功") : ApiResult.failure("归档失败");
    }

}