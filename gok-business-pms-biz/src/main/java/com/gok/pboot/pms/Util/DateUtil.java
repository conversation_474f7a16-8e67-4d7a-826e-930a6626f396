package com.gok.pboot.pms.Util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.components.excel.kit.ExcelException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * 日期转换前端展示
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
public class DateUtil {

    private DateUtil() {}

    /**
     * 年月格式
     */
    public static final String YEAR_MONTH = "yyyyMM";

    /**
     * 年月日格式
     */
    public static final String YEAR_MONTH_DAY = "yyyyMMdd";

    /**
     * 年-月-日 秒级格式
     */
    public static final String SIMPLE_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 年/月/日 秒级格式
     */
    public static final String SIMPLE_DATE_FORMAT_2 = "yyyy/MM/dd";

    /**
     * 年-月-日 时-分-秒 毫秒级格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 年-月-日 时:分
     */
    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm";

    /**
     * 时分秒初始值
     */
    public static final String BLACK_HOUR_MINUTE_SECOND = " 00:00:00";

    /**
     * 时区
     */
    public static final String GMT_8 = "GMT+8";

    /**
     * 每月平均30天
     */
    public static final Integer THIRTY_DAYS = 30;

    /**
     * 转换日期 年月 与 -
     * 2023年 9月 -> 2023-09-00; 2023年10月 -> 2023-10-00
     * 2023-09 -> 2023年 9月; 2023-10 -> 2023年10月
     *
     * @param time 日期
     * @return 日期
     */
    public static String dateTrans(String time) {
        if (!Optional.ofNullable(time).isPresent()) {
            return CharSequenceUtil.EMPTY;
        }
        if (time.contains("年")) {
            if (NumberUtils.INTEGER_ONE.equals(time.substring(time.indexOf("年") + 1, time.indexOf("月")).trim().length())) {
                time = time.substring(0, time.indexOf("年") + 1).replace('年','-') + "0"
                        + time.substring(time.indexOf("年") + 2).replace('月','-') + "00";
            } else {
                time = time.replace('年','-').replace('月','-') + "00";
            }
        } else {
            if ('0' == (time.charAt(time.length() - 1))) {
                time = time.substring(0, time.indexOf("-") + 1).replace('-','年')
                        + time.substring(time.indexOf("-") + 1) + "月";
            } else {
                time = time.substring(0, time.indexOf("-") + 1).replace('-','年')
                        + time.substring(time.indexOf("-") + 1).replace("0"," ") + "月";
            }
        }

        return time;
    }

    /**
     * 日期转换
     * 2023/10月/1日 -> 2023/10/01
     * 2023/ 8月/1日 -> 2023/08/01
     *
     * @param time 时间
     * @return 时间
     */
    public static String signingDateTrans(String time) {
        // 时间为空直接返回
        if (!Optional.ofNullable(time).isPresent()) {
            return CharSequenceUtil.EMPTY;
        }
        if (time.charAt(5) == '0') {
            time = time.substring(0, 5) + time.substring(6, time.length());
        }
        return time;
    }

    /**
     * 判断当前时间与收款日期的差距天数
     *
     * @param paymentDate 收款日期
     * @return 差距天数
     */
    public static int difference(String paymentDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(SIMPLE_DATE_FORMAT);
        // 收款日期转换时间格式初始化
        Date date = null;
        // 当前时间
        Date now = new Date();
        try {
            date = simpleDateFormat.parse(paymentDate);
            now = simpleDateFormat.parse(simpleDateFormat.format(now));
        } catch (ParseException e) {
            log.error("时间格式化有误");
            e.printStackTrace();
        }
        if (null == date) {
            log.error("时间解析有误");
            return THIRTY_DAYS + NumberUtils.INTEGER_ONE;
        }

        return (int) ((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 导入时将日期格式化
     * 比如: 2023/6/6 -> 2023/06/06
     *
     * @param date 导入表的日期
     * @return String 日期
     */
    public static String formatTime(String date) {
        if (!date.contains("/")) {
            throw new ExcelException("日期格式有误,请以模板日期格式重新导入!");
        }
        String[] strings = date.split("/");

        for (int i = 1; i < strings.length; ++i) {
            if (strings[i].length() == 1) {
                strings[i] = "0" + strings[i];
            }
        }
        return strings[0] + "/" + strings[1] + "/" + strings[2];
    }

    /**
     * 日期格式校验yyyy-MM-dd
     *
     * @param date 日期
     * @return {@link Boolean}
     */
    public static Boolean isValidDate(String date) {
        return date.matches("\\d{4}-(\\d{2})(?:-\\d{2})?");
    }


    /**
     * 日期格式校验，支持xxxx-xx-xx、xxxx/xx/xx、xxxx/xx、xxxx-x、xxxx/x/x等格式
     *
     * @param date 日期
     * @return {@link Boolean}
     */
    public static Boolean isValidDateAll(String date) {
        return date.matches("\\d{4}[-/]([1-9]|0[1-9]|1[0-2])(?:[-/]([1-9]|0[1-9]|[12]\\d|3[01])?)?");
    }

    /**
     * 统一日期格式为 xxxx-xx 或 xxxx-xx-xx
     *
     * @param date 日期字符串
     * @return 标准格式的日期字符串
     */
    public static String standardizeDateFormat(String date) {
        if (StrUtil.isBlank(date)) {
            return date;
        }

        String[] parts = date.split("[-/]");
        if (parts.length < 2) {
            return date;
        }

        // 处理年份
        StringBuilder result = new StringBuilder(parts[0]);
        result.append("-");

        // 处理月份
        result.append(String.format("%02d", Integer.parseInt(parts[1])));

        // 处理日期（如果存在）
        if (parts.length > 2) {
            result.append("-")
                    .append(String.format("%02d", Integer.parseInt(parts[2])));
        }

        return result.toString();
    }
}
