package com.gok.pboot.pms.cost.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.gok.components.excel.annotation.RequestExcel;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementDetailVO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskTopCountMsgVO;
import com.gok.pboot.pms.cost.excel.SelectedSheetWriteHandler;
import com.gok.pboot.pms.cost.service.ICostIncomeSettlementDetailService;
import com.gok.pboot.pms.cost.service.ICostIncomeSettlementService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 交付管理-收入结算前端控制器
 *
 * <AUTHOR>
 * @menu 交付管理-收入结算
 * @create 2025/02/18
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/costIncomeSettlement")
public class CostIncomeSettlementController {

    private final ICostIncomeSettlementDetailService settlementDetailService;
    private final ICostIncomeSettlementService settlementService;

    /**
     * 明细列表查询接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @PostMapping("/details/list")
    public ApiResult<List<CostIncomeSettlementDetailVO>> findDetailList(@RequestBody CostIncomeSettlementListDTO dto) {
        return ApiResult.success(settlementDetailService.findDetailList(dto));
    }

    /**
     * 生成或更新明细列表接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @PostMapping("/details/addOrUpdate")
    public ApiResult<String> addOrUpdateCalculationDetails(@RequestBody List<CostIncomeSettlementDetailsEditDTO> dtoList) {
        settlementDetailService.addOrUpdate(dtoList);
        return ApiResult.success("操作成功");
    }

    /**
     * 删除结算明细
     *
     * @param ids
     * @return {@link ApiResult}<{@link String}>
     */
    @DeleteMapping("/detail/delete")
    public ApiResult<String> detailDelete(@RequestBody List<Long> ids) {
        return settlementDetailService.delete(ids);
    }

    /**
     * 导出Excel选中数据
     *
     * @param dto {@link CostIncomeSettlementListDTO}
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(name = "收入结算")
    public void exportExpensesShare(HttpServletResponse response, @RequestBody CostIncomeSettlementListDTO dto) {
        settlementDetailService.export(response,dto);
    }

    /**
     * 导出收入结算明细导入模板
     *
     * @param response
     */
    @GetMapping("/details/excel-model")
    public void excelModel(HttpServletResponse response) throws IOException {
        final String fileName = "收入结算明细导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(CostIncomeSettlementDetailsImportDTO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(CostIncomeSettlementDetailsImportDTO.class))
                .sheet("结算_明细").doWrite(Collections.singletonList(emptyList));
    }

    /**
     * 导入收入结算明细
     * @param projectId
     * @param importDTOList
     * @return
     */
    @PostMapping("/details/import")
    public ApiResult<String> importDetailExcel(@NotNull(message = "项目id不能为空") @PathParam("projectId") Long projectId,
                                 @RequestExcel List<CostIncomeSettlementDetailsImportDTO> importDTOList) {
        return settlementDetailService.importExcel(projectId,importDTOList);
    }


    /**
     * 汇总列表查询接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @PostMapping("/list")
    public ApiResult<List<CostIncomeSettlementVO>> findList(@RequestBody CostIncomeSettlementListDTO dto) {
        return ApiResult.success(settlementService.findList(dto));
    }

    /**
     * 生成或更新汇总列表接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @PostMapping("/addOrUpdate")
    public ApiResult<String> addOrUpdate(@RequestBody List<CostIncomeSettlementEditDTO> dtoList) {
        settlementService.addOrUpdate(dtoList);
        return ApiResult.success("操作成功");
    }

    /**
     * 导出收入结算汇总导入模板
     *
     * @param response
     */
    @GetMapping("/excel-model")
    public void model(HttpServletResponse response) throws IOException {
        final String fileName = "收入结算汇总导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(CostIncomeSettlementImportDTO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(CostIncomeSettlementImportDTO.class))
                .sheet("结算_汇总").doWrite(Collections.singletonList(emptyList));
    }

    /**
     * 导入收入结算汇总
     * @param projectId
     * @param importDTOList
     * @return
     */
    @PostMapping("/import")
    public ApiResult<String> importExcel(@NotNull(message = "项目id不能为空") @PathParam("projectId") Long projectId,
                                         @RequestExcel List<CostIncomeSettlementImportDTO> importDTOList) {
        return settlementService.importExcel(projectId,importDTOList);
    }

    /**
     * 删除结算汇总
     *
     * @param ids
     * @return {@link ApiResult}<{@link String}>
     */
    @DeleteMapping("/delete")
    public ApiResult<String> delete(@RequestBody List<Long> ids) {
        return settlementService.delete(ids);
    }

    /**
     * 发起结算审批流程
     *
     * @param dto 项目里程碑发起流程Dto
     * @return String
     */
    @ApiOperation(value = "发起结算审批流程", notes = "发起结算审批流程")
    @PostMapping("/createSettlementRequest")
    public R<String> createSettlementRequest(@RequestBody CostIncomeSettlementRequestDTO dto) {
        return settlementService.createSettlementRequest(dto);
    }


    /**
     * 判断是否隐藏【结算审批】
     * @param projectId
     * @return
     */
    @GetMapping("/isCreateSettlementRequest/{projectId}")
    public ApiResult<Boolean> isCreateSettlementRequest(@PathVariable String projectId) {
        return settlementService.isCreateSettlementRequest(projectId);
    }
}
