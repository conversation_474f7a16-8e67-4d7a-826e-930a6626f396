package com.gok.pboot.pms.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.excel.handler.ListAnalysisEventListener;
import com.gok.components.excel.kit.Validators;
import com.gok.components.excel.vo.ErrorMessage;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自定义Excel导入监听器
 * 支持自定义跳过行数
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
public class PersonImportInfoEventListener extends ListAnalysisEventListener<Object> {

    private final List<Object> list = new ArrayList<>();
    private final List<ErrorMessage> errorMessageList = new ArrayList<>();
    private Long lineNum = 1L;
    private Long skipRows = 2L;

    @Override
    public void invoke(Object data, AnalysisContext analysisContext) {
        lineNum++;
        if (lineNum <= skipRows){
            return;
        }
        Set<ConstraintViolation<Object>> violations = Validators.validate(data);
        if (!violations.isEmpty()){
            Set<String> messageSet = violations.stream().map(ConstraintViolation::getMessage)
                    .collect(Collectors.toSet());
            errorMessageList.add(new ErrorMessage(lineNum, messageSet));
        } else {
            list.add(data);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.debug("Excel read analysed");
    }

    @Override
    public List<Object> getList() {
        return list;
    }

    @Override
    public List<ErrorMessage> getErrors() {
        return errorMessageList;
    }
} 