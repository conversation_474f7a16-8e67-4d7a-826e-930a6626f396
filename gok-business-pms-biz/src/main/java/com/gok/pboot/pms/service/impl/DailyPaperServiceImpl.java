package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.constant.CommonConstants;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.common.validate.validator.ManHourValidator;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.entity.bo.TaskBo;
import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectTaske;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.dto.DailyPaperAddOrUpdateDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperMonthlyDTO;
import com.gok.pboot.pms.entity.dto.UserPmsDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.DailyPaperAnalyzer;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.IDailyPaperService;
import com.gok.pboot.pms.service.fegin.CenterUserService;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.*;
import static com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum.LAG;
import static com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum.NORMAL;

/**
 * - 日报服务实现类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 9:48
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DailyPaperServiceImpl implements IDailyPaperService {

    private final DailyPaperMapper mapper;
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final TomorrowPlanPaperEntryMapper tomorrowPlanPaperEntryMapper;
    private final HolidayMapper holidayMapper;
    private final FilingMapper filingMapper;
    private final OvertimeLeaveDataMapper overtimeLeaveDataMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final PmsRetriever pmsRetriever;
    private final DailyPaperAnalyzer dailyPaperAnalyzer;
    private final ICompensatoryLeaveDataService overtimeLeaveDataService;
    private final CenterUserService centerUserService;

    private final RosterMapper rosterMapper;

    private final ProjectTaskeMapper projectTaskeMapper;

    private static final String PARAM_UPDATE = "update";

    private final BcpLoggerUtils bcpLoggerUtils;

    private final ICompensatoryLeaveDataService compensatoryLeaveDataService;

    @Override
    public Page<DailyPaper> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        return mapper.findList(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
    }

    @Override
    public DailyPaper getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public DailyPaperDetailsVO getDetailsById(Long id) {
        // 1. 基础数据验证和获取
        DailyPaper dailyPaper = validateAndGetDailyPaper(id);

        // 2. 批量获取相关数据
        DailyPaperDataContext context = buildDailyPaperDataContext(dailyPaper);

        // 3. 构建基础VO对象
        DailyPaperDetailsVO detailsVO = buildBasicDetailsVO(dailyPaper, context);

        // 4. 设置任务状态和审核信息
        enrichDetailsWithTaskAndReviewerInfo(detailsVO, context);

        // 5. 设置归档状态
        setFilingStatus(detailsVO, dailyPaper.getSubmissionDate());

        return detailsVO;
    }

    /**
     * 验证并获取日报基础信息
     */
    private DailyPaper validateAndGetDailyPaper(Long id) {
        DailyPaper dailyPaper = mapper.selectById(id);
        if (dailyPaper == null) {
            throw new ServiceException("没有找到指定日报");
        }
        return dailyPaper;
    }

    /**
     * 构建日报数据上下文，批量获取相关数据
     */
    private DailyPaperDataContext buildDailyPaperDataContext(DailyPaper dailyPaper) {
        Long dailyPaperId = dailyPaper.getId();
        LocalDate submissionDate = dailyPaper.getSubmissionDate();
        Long userId = dailyPaper.getUserId();

        // 并行获取基础数据
        List<DailyPaperEntry> entries = dailyPaperEntryMapper.findByDailyPaperId(dailyPaperId);
        List<TomorrowPlanPaperEntry> tomorrowEntries = tomorrowPlanPaperEntryMapper.findByDailyPaperId(dailyPaperId);

        // 获取项目信息
        Map<Long, ProjectInDailyPaperEntry> projects = findDailyAndTomorrowProjects(entries, tomorrowEntries);

        // 获取用户和假期信息
        String userRealName = rosterMapper.findAliasNameById(userId);
        BigDecimal leaveHour = findLeaveDataByDateAndUserId(submissionDate, userId);
        BigDecimal compensatoryLeave = findCompensatoryLeaveDataByDateAndUserId(submissionDate, userId);
        Holiday holiday = holidayMapper.selByDate(submissionDate);

        return DailyPaperDataContext.builder()
                .entries(entries)
                .tomorrowEntries(tomorrowEntries)
                .projects(projects)
                .userRealName(userRealName)
                .leaveHour(leaveHour)
                .compensatoryLeave(compensatoryLeave)
                .holiday(holiday)
                .build();
    }

    /**
     * 构建基础的详情VO对象
     */
    private DailyPaperDetailsVO buildBasicDetailsVO(DailyPaper dailyPaper, DailyPaperDataContext context) {
        BigDecimal totalLeaveHour = context.getLeaveHour().add(context.getCompensatoryLeave());
        String abnormalDesc = dailyPaperAnalyzer.getDailyPaperAbnormalInfo(dailyPaper, totalLeaveHour)
                .stream()
                .map(Pair::getSecond)
                .collect(Collectors.joining(";"));

        return new DailyPaperDetailsVO(
                dailyPaper,
                context.getEntries(),
                context.getTomorrowEntries(),
                context.getProjects(),
                context.getUserRealName(),
                abnormalDesc,
                context.getLeaveHour(),
                context.getCompensatoryLeave(),
                context.getHoliday()
        );
    }

    /**
     * 丰富详情VO的任务状态和审核人信息
     */
    private void enrichDetailsWithTaskAndReviewerInfo(DailyPaperDetailsVO detailsVO, DailyPaperDataContext context) {
        // 获取所有任务ID
        List<Long> allTaskIds = getAllTaskIds(context.getEntries(), context.getTomorrowEntries());

        // 获取未完成的任务ID
        Set<Long> unfinishedTaskIds = getUnfinishedTaskIds(allTaskIds);

        // 获取审核人信息
        Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable = getReviewerInfoTable(context.getEntries());
        Map<Long, String> userIdAndAliasNameMap = buildUserIdAndAliasNameMap(reviewerInfoTable);

        // 设置任务状态
        setTaskFinishFlags(detailsVO, unfinishedTaskIds);

        // 设置工时类型和审核人信息
        setWorkTypeAndReviewerInfo(detailsVO, reviewerInfoTable, userIdAndAliasNameMap);
    }

    /**
     * 获取所有任务ID
     */
    private List<Long> getAllTaskIds(List<DailyPaperEntry> entries, List<TomorrowPlanPaperEntry> tomorrowEntries) {
        return Stream.concat(
                entries.stream().map(DailyPaperEntry::getTaskId),
                tomorrowEntries.stream().map(TomorrowPlanPaperEntry::getTaskId)
        ).distinct().collect(Collectors.toList());
    }

    /**
     * 获取未完成的任务ID
     */
    private Set<Long> getUnfinishedTaskIds(List<Long> allTaskIds) {
        if (allTaskIds.isEmpty()) {
            return ImmutableSet.of();
        }
        return ObjectUtils.defaultIfNull(
                projectTaskeMapper.findUnFinishedTaskIds(allTaskIds, SecurityUtils.getUser().getId()),
                ImmutableSet.of()
        );
    }

    /**
     * 获取审核人信息表
     */
    private Table<Long, Long, TaskReviewerInfoBO> getReviewerInfoTable(List<DailyPaperEntry> entries) {
        List<Pair<Long, Long>> userTaskPairs = entries.stream()
                .map(e -> Pair.of(e.getUserId(), e.getTaskId()))
                .collect(Collectors.toList());
        return pmsRetriever.getReviewerInfo(userTaskPairs);
    }

    /**
     * 构建用户ID和别名映射
     */
    private Map<Long, String> buildUserIdAndAliasNameMap(Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable) {
        Set<Long> userIds = extractUserIdsFromReviewerInfo(reviewerInfoTable);
        if (userIds.isEmpty()) {
            return ImmutableMap.of();
        }
        return rosterMapper.selectBatchIds(userIds)
                .stream()
                .collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
    }

    /**
     * 从审核人信息中提取用户ID
     */
    private Set<Long> extractUserIdsFromReviewerInfo(Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable) {
        Set<Long> userIds = Sets.newHashSet();
        reviewerInfoTable.values().forEach(r -> {
            Set<TaskReviewerTypeEnum> types = r.getTypes();
            if (types.contains(TaskReviewerTypeEnum.TASK_LEADER)) {
                userIds.addAll(r.getTaskLeaderUserIds());
            }
            if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
                userIds.add(r.getProjectManagerUserId());
                userIds.add(r.getProjectPreSalesmanUserId());
                userIds.add(r.getProjectSalesmanUserId());
            }
            if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER)) {
                userIds.add(r.getDirectLeaderUserId());
            }
        });
        return userIds;
    }

    /**
     * 设置任务完成标志
     */
    private void setTaskFinishFlags(DailyPaperDetailsVO detailsVO, Set<Long> unfinishedTaskIds) {
        detailsVO.getEntries().forEach(e -> e.setTaskFinishFlag(!unfinishedTaskIds.contains(e.getTaskId())));
        detailsVO.getTomorrowPlanPaperEntries().forEach(e -> e.setTaskFinishFlag(!unfinishedTaskIds.contains(e.getTaskId())));
    }

    /**
     * 设置工时类型和审核人信息
     */
    private void setWorkTypeAndReviewerInfo(DailyPaperDetailsVO detailsVO,
                                          Table<Long, Long, TaskReviewerInfoBO> reviewerInfoTable,
                                          Map<Long, String> userIdAndAliasNameMap) {
        // 设置日报条目的工时类型和审核人
        detailsVO.getEntries().forEach(entry -> {
            setWorkTypeTxt(entry.getWorkType(), entry::setWorkTypeTxt);
            setAuditorsInDailyPaperEntry(entry, reviewerInfoTable, userIdAndAliasNameMap);
        });

        // 设置明日计划条目的工时类型
        detailsVO.getTomorrowPlanPaperEntries().forEach(entry -> {
            setWorkTypeTxt(entry.getWorkType(), entry::setWorkTypeTxt);
        });
    }

    /**
     * 设置工时类型文本
     */
    private void setWorkTypeTxt(Integer workType, Consumer<String> setter) {
        if (EnumUtils.existsEnumValue(workType, WorkHourEnum.class)) {
            setter.accept(EnumUtils.getNameByValue(WorkHourEnum.class, workType));
        }
    }

    /**
     * 设置归档状态
     */
    private void setFilingStatus(DailyPaperDetailsVO detailsVO, LocalDate submissionDate) {
        boolean isFiled = Optional.ofNullable(submissionDate)
                .map(date -> YesOrNoEnum.YES.getValue().equals(filingMapper.isFiledByDate(date)))
                .orElse(false);
        detailsVO.getDailyPaper().setIfFiled(isFiled);
    }

    /**
     * 设置日报条目的主审人字段
     *
     * @param entry                 日报条目
     * @param reviewerInfoBOTable   主审人信息
     * @param userIdAndAliasNameMap 用户id和姓名的映射
     */
    private void setAuditorsInDailyPaperEntry(
            DailyPaperEntryVO entry,
            Table<Long, Long, TaskReviewerInfoBO> reviewerInfoBOTable,
            Map<Long, String> userIdAndAliasNameMap
    ) {
        TaskReviewerInfoBO reviewerInfoBO = reviewerInfoBOTable.get(entry.getUserId(), entry.getTaskId());
        List<String> auditorNames = new ArrayList<>();
        Set<TaskReviewerTypeEnum> types;
        String name;

        if (reviewerInfoBO == null) {
            return;
        }
        types = reviewerInfoBO.getTypes();
        if (types.contains(TaskReviewerTypeEnum.TASK_LEADER)) {
            reviewerInfoBO.getTaskLeaderUserIds().forEach(uId -> {
                String aName = userIdAndAliasNameMap.get(uId);

                if (StringUtils.isNotBlank(aName)) {
                    auditorNames.add(aName);
                }
            });
        }
        if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectSalesmanUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectManagerUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getProjectPreSalesmanUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
        }
        if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER)) {
            name = userIdAndAliasNameMap.get(reviewerInfoBO.getDirectLeaderUserId());
            if (StringUtils.isNotBlank(name)) {
                auditorNames.add(name);
            }
        }
        if (!auditorNames.isEmpty()) {
            entry.setAuditorNames(auditorNames);
        }
    }

    @Override
    public DailyPaperDetailsVO getDetailsByDateForCurrentUser(LocalDate date) {
        Long userId = SecurityUtils.getUser().getId();
        DailyPaper dailyPaper = mapper.findOneBeforeBySubmissionDateAndUserId(date, userId);
        DailyPaperDetailsVO result;

        if (dailyPaper == null) {
            throw new ServiceException("没有找到目标日报");
        }
        result = buildDailyPaperDetailsVO(dailyPaper);
        // 将ID全部置空，防止前端在新增日报接口误传
        result.getDailyPaper().setId(null);
        result.getEntries().forEach(e -> {
            e.setId(null);
            e.setApprovalStatus(WTB.getValue());
            e.setApprovalStatusName(WTB.getName());
        });

        return result;
    }

    @Override
    public DailyPaperDetailsVO getDetailsByDateForCurrentUserAccurate(LocalDate date) {
        Long userId = SecurityUtils.getUser().getId();
        Long paperId = mapper.findIdBySubmissionDateAndUserId(date, userId);
        BigDecimal leaveHour;
        DailyPaperVO dailyPaper;
        DailyPaperDetailsVO result;

        if (paperId == null) {
            // 查询请假信息
            leaveHour = findLeaveDataByDateAndUserId(date, userId);
            dailyPaper = new DailyPaperVO();
            dailyPaper.setLeaveHourData(leaveHour);
            // 查询调休数据
            BigDecimal compensatoryLeave = findCompensatoryLeaveDataByDateAndUserId(date, userId);
            dailyPaper.setCompensatoryHourCount(compensatoryLeave);
            result = new DailyPaperDetailsVO();
            result.setDailyPaper(dailyPaper);

            return result;
        }

        return getDetailsById(paperId);
    }

    /**
     * 根据日期和用户ID获取请休假数据
     *
     * @param date   日期
     * @param userId 用户ID
     * @return 请休假数据
     */
    private BigDecimal findLeaveDataByDateAndUserId(LocalDate date, Long userId) {
        List<OvertimeLeaveData> leaveDataList = overtimeLeaveDataMapper.getLeaveDataListByUserId(date, userId);
        BigDecimal leaveHour = BigDecimal.ZERO;

        for (OvertimeLeaveData l : leaveDataList) {
            if (EnumUtils.valueEquals(l.getType(), LeaveStatusEnum.XJ)) {
                leaveHour = leaveHour.subtract(l.getHourData());
            } else {
                leaveHour = leaveHour.add(l.getHourData());
            }
        }

        return BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHour) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : leaveHour;
    }

    /**
     * 根据日期和用户ID获取项目调休数据
     *
     * @param date   日期
     * @param userId 用户ID
     * @return 请休假数据
     */
    private BigDecimal findCompensatoryLeaveDataByDateAndUserId(LocalDate date, Long userId) {
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        List<LocalDate> holidayDateList = holidayList.stream().map(Holiday::getDayDate).collect(Collectors.toList());

        //查询调休信息
        List<CompensatoryLeaveDataVO> compensatoryLeaveList =
                overtimeLeaveDataService.findByDateTimeRangeAndUserId(date,date.plusDays(1),userId)
                .stream().filter(e -> !holidayDateList.contains(e.getBelongDate())).collect(Collectors.toList());
        Map<LocalDate, List<CompensatoryLeaveDataVO>> compensatoryLeaveMap = compensatoryLeaveList.stream().collect(Collectors.groupingBy(CompensatoryLeaveDataVO::getBelongDate));

        BigDecimal compensatoryLeaveHour = BigDecimal.ZERO;
        List<CompensatoryLeaveDataVO> voList = compensatoryLeaveMap.getOrDefault(date, null);
        if (CollectionUtils.isNotEmpty(voList)){
            compensatoryLeaveHour = voList.stream()
                    .map(CompensatoryLeaveDataVO::getHourData)
                    .reduce(BigDecimal.ZERO,BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimalUtils.SEVEN_DECIMAL.compareTo(compensatoryLeaveHour) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : compensatoryLeaveHour;
    }
    /**
     * 查询日报条目和明日计划条目的项目
     *
     * @param entries
     * @param tomorrowPlanPaperEntries
     * @return
     */
    private Map<Long, ProjectInDailyPaperEntry> findDailyAndTomorrowProjects(List<DailyPaperEntry> entries, List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntries) {
        List<Long> projectIds = new ArrayList<>();
        List<Long> tomorrowPlanPaperEntryIds = tomorrowPlanPaperEntries.stream()
                .map(TomorrowPlanPaperEntry::getProjectId)
                .collect(Collectors.toList());
        List<Long> entryIds = entries.stream()
                .map(DailyPaperEntry::getProjectId)
                .collect(Collectors.toList());
        projectIds.addAll(tomorrowPlanPaperEntryIds);
        projectIds.addAll(entryIds);
        List<Long> distinctProjectIds = projectIds.stream().distinct().collect(Collectors.toList());
        return projectInfoMapper.findByIdsForDailyPaperEntry(distinctProjectIds)
                .stream()
                .collect(Collectors.toMap(ProjectInDailyPaperEntry::getId, project -> project));
    }

    private DailyPaperDetailsVO buildDailyPaperDetailsVO(DailyPaper dailyPaper) {
        List<DailyPaperEntry> entries;
        List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntries;
        Map<Long, ProjectInDailyPaperEntry> projects;
        List<Long> allTaskIds;
        Set<Long> taskIdsUnfinished;

        Long paperId = dailyPaper.getId();
        entries = dailyPaperEntryMapper.findByDailyPaperId(paperId);
        tomorrowPlanPaperEntries = tomorrowPlanPaperEntryMapper.findByDailyPaperId(paperId);
        allTaskIds = Lists.newArrayListWithCapacity(entries.size() + tomorrowPlanPaperEntries.size());
        entries.forEach(e -> allTaskIds.add(e.getTaskId()));
        tomorrowPlanPaperEntries.forEach(e -> allTaskIds.add(e.getTaskId()));
        taskIdsUnfinished = allTaskIds.isEmpty() ? ImmutableSet.of() : projectTaskeMapper.findUnFinishedTaskIds(
                allTaskIds,
                SecurityUtils.getUser().getId()
        );
        // 需要额外将Project查出来，去取这些字段
        projects = this.findDailyAndTomorrowProjects(entries, tomorrowPlanPaperEntries);

        DailyPaperDetailsVO dailyPaperDetailsVO = new DailyPaperDetailsVO(dailyPaper, entries, tomorrowPlanPaperEntries, projects);
        List<DailyPaperEntryVO> voEntries = dailyPaperDetailsVO.getEntries();
        List<TomorrowPlanPaperEntryVO> voTomorrowPlanPaperEntries = dailyPaperDetailsVO.getTomorrowPlanPaperEntries();
        // 设置工时类型txt、工时是否结束
        if (CollUtil.isNotEmpty(voEntries)) {
            voEntries.forEach(entry -> {
                if (EnumUtils.existsEnumValue(entry.getWorkType(), WorkHourEnum.class)) {
                    entry.setWorkTypeTxt(EnumUtils.getNameByValue(WorkHourEnum.class, entry.getWorkType()));
                }
                if (taskIdsUnfinished.contains(entry.getTaskId())) {
                    entry.setTaskFinishFlag(false);
                } else {
                    entry.setTaskFinishFlag(true);
                    entry.setProjectId(null);
                    entry.setTaskId(null);
                }
            });
        }
        if (CollUtil.isNotEmpty(voTomorrowPlanPaperEntries)) {
            voTomorrowPlanPaperEntries.forEach(tomorrowPlanPaperEntry -> {
                if (EnumUtils.existsEnumValue(tomorrowPlanPaperEntry.getWorkType(), WorkHourEnum.class)) {
                    tomorrowPlanPaperEntry.setWorkTypeTxt(EnumUtils.getNameByValue(WorkHourEnum.class, tomorrowPlanPaperEntry.getWorkType()));
                }
                if (taskIdsUnfinished.contains(tomorrowPlanPaperEntry.getTaskId())) {
                    tomorrowPlanPaperEntry.setTaskFinishFlag(false);
                } else {
                    tomorrowPlanPaperEntry.setTaskFinishFlag(true);
                    tomorrowPlanPaperEntry.setProjectId(null);
                    tomorrowPlanPaperEntry.setTaskId(null);
                }
            });
        }

        return dailyPaperDetailsVO;
    }

    /**
     * ~ 添加/编辑 日报 ~
     *
     * @param request 请求实体
     * <AUTHOR>
     * @date 2022/8/24 9:58
     */
    @Override
    @Transactional
    public void newAddOrUpdate(DailyPaperAddOrUpdateDTO request) {
        DailyPaper addOrUpdate;
        Map<String, List<DailyPaperEntry>> entries;
        List<Long> entriesNeedRemove;
        List<Long> tomorrowEntriesNeedRemove;
        List<DailyPaperEntry> add;
        List<DailyPaperEntry> update;

        // 明日计划
        Map<String, List<TomorrowPlanPaperEntry>> tomorrowPlanEntries;
        List<TomorrowPlanPaperEntry> tomorrowPlanAdd;
        List<TomorrowPlanPaperEntry> tomorrowPlanUpdate;

        validateDailyPaperAddOrUpdateDTO(request);
        if (request.getId() == null) {
            Timestamp submissionTime = null;
            if (request.getSubmit()) {
                //初始化提交时间
                submissionTime = new Timestamp(System.currentTimeMillis());
            }
            // add
            addOrUpdate = tryCreateDailyPaper(request, submissionTime);
            // 添加时手动将条目ID置空，防止前端传参错误，意外触发更新逻辑
            request.getEntries().forEach(e -> e.setId(null));
            request.getTomorrowPlanPaperEntries().forEach(e -> e.setId(null));

            entries = tryCreateDailyPaperEntryList(request, addOrUpdate);
            add = entries.get("add");
            update = entries.get(PARAM_UPDATE);
            // 明日计划
            tomorrowPlanEntries = tryCreateTomorrowPlanPaperEntryList(request, addOrUpdate);
            tomorrowPlanAdd = tomorrowPlanEntries.get("add");
            tomorrowPlanUpdate = tomorrowPlanEntries.get(PARAM_UPDATE);

            // 如果用户传条目参数时给定了id（传参不规范），则本应add的数据就会出现在update中，这时需要将其重新分配ID，添加到add中
            update.forEach(entry -> {
                BaseBuildEntityUtil.buildInsert(entry);
                add.add(entry);
            });
            tomorrowPlanUpdate.forEach(entry -> {
                BaseBuildEntityUtil.buildInsert(entry);
                tomorrowPlanAdd.add(entry);
            });

            // 插入日报数据
            mapper.insert(addOrUpdate);
            // 插入日报条目数据
            if (CollectionUtils.isNotEmpty(add)) {
                dailyPaperEntryMapper.batchSave(add);
            }
            // 插入明日计划条目数据
            if (CollectionUtils.isNotEmpty(tomorrowPlanAdd)) {
                tomorrowPlanPaperEntryMapper.batchSave(tomorrowPlanAdd);
            }
        } else {
            // update
            addOrUpdate = tryGetDailyPaperForUpdate(request);
            entries = tryCreateDailyPaperEntryList(request, addOrUpdate);
            add = entries.get("add");
            update = entries.get(PARAM_UPDATE);
            // 明日计划
            tomorrowPlanEntries = tryCreateTomorrowPlanPaperEntryList(request, addOrUpdate);
            tomorrowPlanAdd = tomorrowPlanEntries.get("add");
            tomorrowPlanUpdate = tomorrowPlanEntries.get(PARAM_UPDATE);
            // 找到传来的新日报中没有的条目数据，这些是要删除的
            entriesNeedRemove = dailyPaperEntryMapper.findByDailyPaperId(addOrUpdate.getId())
                    .stream()
                    .filter(e -> {
                        Long id = e.getId();
                        Predicate<DailyPaperEntry> predicate = entry -> entry.getId().equals(id);

                        return entries.get("add").stream().noneMatch(predicate) &&
                                entries.get(PARAM_UPDATE).stream().noneMatch(predicate) &&
                                // 如果遇到需要删除的数据是“已通过”状态，不进行删除，将这个数据保留处理（暂时这么处理，这种情况只会出现在请求体被篡改的情况下）
                                !YTG.getValue().equals(e.getApprovalStatus());
                    })
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList());
            // 明日计划
            tomorrowEntriesNeedRemove = tomorrowPlanPaperEntryMapper.findByDailyPaperId(addOrUpdate.getId())
                    .stream()
                    .filter(e -> {
                        Long id = e.getId();
                        Predicate<TomorrowPlanPaperEntry> predicate = entry -> entry.getDailyPaperId().equals(id);

                        return tomorrowPlanEntries.get("add").stream().noneMatch(predicate) &&
                                tomorrowPlanEntries.get(PARAM_UPDATE).stream().noneMatch(predicate) &&
                                // 如果遇到需要删除的数据是“已通过”状态，不进行删除，将这个数据保留处理（暂时这么处理，这种情况只会出现在请求体被篡改的情况下）
                                !YTG.getValue().equals(e.getApprovalStatus());
                    })
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList());

            mapper.updateById(addOrUpdate);
            // 保存请求新增的
            if (!add.isEmpty()) {
                dailyPaperEntryMapper.batchSave(entries.get("add"));
            }
            // 明日计划
            if (!tomorrowPlanAdd.isEmpty()) {
                tomorrowPlanPaperEntryMapper.batchSave(tomorrowPlanEntries.get("add"));
            }
            // 修改原本已有的
            if (!update.isEmpty()) {
                dailyPaperEntryMapper.batchUpdate(entries.get(PARAM_UPDATE));
            }
            // 明日计划
            if (!tomorrowPlanUpdate.isEmpty()) {
                tomorrowPlanPaperEntryMapper.batchUpdate(tomorrowPlanEntries.get(PARAM_UPDATE));
            }
            // 删除请求中没有，但原本有的
            if (!entriesNeedRemove.isEmpty()) {
                dailyPaperEntryMapper.deleteBatchIds(entriesNeedRemove);
            }
            // 明日计划
            if (!tomorrowEntriesNeedRemove.isEmpty()) {
                tomorrowPlanPaperEntryMapper.deleteBatchIds(tomorrowEntriesNeedRemove);
            }
            // 应对这种情况：用户日报中原本有“已通过”条目和其他类型的条目，用户更新日报，删除了其他类型的条目，这时日报状态也需要更新
            updateApprovalStatusIfNeeded(ImmutableList.of(addOrUpdate.getId()), YTG);
            if (request.getSubmit()){
                bcpLoggerUtils.log(FunctionConstants.REPORTING_OF_WORKING_HOURS, LogContentEnum.SUBMIT_DAILY_REPORT,request.getSubmissionDate());
            }else{
                bcpLoggerUtils.log(FunctionConstants.REPORTING_OF_WORKING_HOURS, LogContentEnum.SAVE_DAILY_REPORT,request.getSubmissionDate());

            }

        }
    }

    /**
     * ~ 验证添加/编辑 日报 的DTO对象是否合法 ~
     *
     * @param dto dto
     * <AUTHOR>
     * @date 2022/8/24 11:15
     */
    private void validateDailyPaperAddOrUpdateDTO(DailyPaperAddOrUpdateDTO dto) {
        LocalDate nowLocalDate = LocalDate.now();
        LocalDate submissionDate = dto.getSubmissionDate();
        List<DailyPaperAddOrUpdateDTO.DailyPaperEntry> entries;
        Set<String> projectTaskTypeSet;
        BigDecimal sumHours, sumNormalHours;
        List<Long> taskIds;
        Set<Long> taskIdsUnfinished;

        if (nowLocalDate.isBefore(submissionDate)) {
            throw new ServiceException("不能填写未来的日报，请修改提交时间");
        }
        if (dto.getId() == null &&
                !mapper.findBySubmissionDateAndUserId(submissionDate, SecurityUtils.getUser().getId()).isEmpty()
        ) {
            throw new ServiceException("添加日报失败，目标日期下已存在日报");
        }

        entries = dto.getEntries();
        if (dto.getSubmit() && CollectionUtils.isEmpty(entries)) {
            throw new ServiceException("日报条目不能为空");
        }
        projectTaskTypeSet = Sets.newHashSetWithExpectedSize(entries.size());
        sumHours = BigDecimal.ZERO;
        sumNormalHours = BigDecimal.ZERO;
        for (DailyPaperAddOrUpdateDTO.DailyPaperEntry e : entries) {
            //由于部署前不存在worktype这个字段导致有的人回退后的项目，因为通过的没有这个无法提交(暂时改成这样，后续可调整回去)
            int workType;
            if (YesOrNoEnum.NO.getValue().equals(e.getIsInsideProject()) && ObjectUtil.isEmpty(e.getWorkType())) {
//                throw new ServiceException("外部项目对应的工时类型不能为空");
                workType = -1;
            } else {
                workType = YesOrNoEnum.NO.getValue().equals(e.getIsInsideProject()) ? e.getWorkType() : 2;
            }
            String bigKey = "" + e.getProjectId() + e.getTaskId() + workType;

            if (dto.getSubmit()) {
                if (e.getNormalHours().compareTo(BigDecimal.ZERO) == 0 && e.getAddedHours().compareTo(BigDecimal.ZERO) == 0) {
                    throw new ServiceException("存在工时总计为0的日报条目，请认真检查");
                }
                if (projectTaskTypeSet.contains(bigKey)) {
                    throw new ServiceException("信息异常，项目+任务+工时类型不可重复");
                }
            }

            projectTaskTypeSet.add(bigKey);
            sumHours = sumHours.add(e.getNormalHours()).add(e.getAddedHours());
            sumNormalHours = sumNormalHours.add(e.getNormalHours());
        }

        if (sumHours.compareTo(ManHourValidator.TWENTY_FOUR_DECIMAL) > 0) {
            throw new ServiceException("当日填入工时数≥24小时，请确认后再提交");
        }
        if (sumNormalHours.compareTo(new BigDecimal("7")) > 0) {
            throw new ServiceException("日常工时总和不能超过7小时，请确认后再提交");
        }
        if (YesOrNoEnum.YES.getValue().equals(filingMapper.isFiledByDate(submissionDate))) {
            throw new ServiceException("当前填报时间内存在归档日期，请重新选择或联系管理员！");
        }
        taskIds = dto.getEntries().stream().filter(d -> !YTG.getValue().equals(d.getApprovalStatus())).map(DailyPaperAddOrUpdateDTO.DailyPaperEntry::getTaskId).distinct().collect(Collectors.toList());
        if (taskIds.isEmpty()) {
            return;
        }
        taskIdsUnfinished = projectTaskeMapper.findUnFinishedTaskIds(taskIds, SecurityUtils.getUser().getId());
        if (taskIdsUnfinished.size() != taskIds.size()) {
            throw new ServiceException("您填报的任务已结束，请重新选择或联系相关负责人");
        }
    }

    /**
     * ~ 根据DTO创建用于添加的日报对象 ~
     *
     * @param dto dto
     * @return com.gok.pboot.pms.entity.DailyPaper
     * <AUTHOR>
     * @date 2022/8/24 15:45
     */
    private DailyPaper tryCreateDailyPaper(DailyPaperAddOrUpdateDTO dto, Timestamp submissionTime) {
        Integer approvalStatus;
        Long userId = SecurityUtils.getUser().getId();
        DailyPaper dailyPaper;
        List<DailyPaperAddOrUpdateDTO.DailyPaperEntry> entries;
        Set<Long> projectIds;
        LocalDate submissionDate;
        dailyPaper = new DailyPaper();
        entries = dto.getEntries();
        projectIds = Sets.newHashSetWithExpectedSize(entries.size());
        submissionDate = dto.getSubmissionDate();
        entries.forEach(e -> projectIds.add(e.getProjectId()));

        //根据submissionTime判定滞后，没有提交时间是正常的
        if (Optional.ofNullable(submissionTime).isPresent()) {
            dailyPaper.setSubmissionTime(submissionTime);
            //当前是提交日报，且日报没有晚于第二天14点都是正常的，不然算滞后
            dailyPaper.setFillingState(isLag(submissionDate, Instant.ofEpochMilli(submissionTime.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime()) ? LAG.getValue() : NORMAL.getValue());
        } else {
            dailyPaper.setFillingState(NORMAL.getValue());
        }
        Roster userInfo = rosterMapper.selectById(userId);

        approvalStatus = dto.getSubmit() ?
                DSH.getValue()
                :
                // 如果不提交，要判断条目是否为空，据此决定日报是未提交还是未填报
                CollectionUtils.isEmpty(dto.getEntries()) ?
                        WTB.getValue()
                        :
                        WTJ.getValue();
        dailyPaper.setUserId(userId)
                .setUserStatus(userInfo == null ? PersonnelStatusEnum.ZS.getValue() :
                        PersonnelStatusEnum.getByEmployeeStatusEnum(EnumUtils.getEnumByValue(
                                EmployeeStatusEnum.class,
                                userInfo.getEmployeeStatus()
                        )).getValue()
                )
                .setUserDeptId(userInfo == null ? null : userInfo.getDeptId())
                .setSubmissionDate(submissionDate)
                // 判断是否为工作日，此处先写死为否
                .setWorkday(holidayMapper.exists(submissionDate) ? BaseConstants.NO : BaseConstants.YES)
                .setApprovalStatus(approvalStatus)
                .setProjectCount(projectIds.size())
                .setTaskCount(entries.size())
                .setDailyHourCount(
                        entries.stream()
                                .map(DailyPaperAddOrUpdateDTO.DailyPaperEntry::getNormalHours)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO)
                )
                .setAddedHourCount(
                        entries.stream()
                                .map(DailyPaperAddOrUpdateDTO.DailyPaperEntry::getAddedHours)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO)
                );
        BaseBuildEntityUtil.buildInsert(dailyPaper);

        return dailyPaper;
    }

    /**
     * ~ 判断指定日期是否是滞后提交的日期 ~
     *
     * @param firstSubmitDate 日期
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/19 9:59
     */
    public static boolean isLag(LocalDate submit, LocalDateTime firstSubmitDate) {
        long until = submit.until(firstSubmitDate, ChronoUnit.DAYS);

        if (until > 1) {
            return true;
        } else if (until == 0) {
            return false;
        } else {
            // util == 1：如果当前正好处在日报日期的第二天，则判断是否已经过了下午两点
            return firstSubmitDate.getHour() >= 14;
        }
    }

    /**
     * ~ 根据所给添加日报DTO获取项目 ~
     *
     * @param dto dto
     * @return com.gok.pboot.pms.entity.Project
     * <AUTHOR>
     * @date 2022/8/24 11:22
     */
    private DailyPaper tryGetDailyPaperForUpdate(DailyPaperAddOrUpdateDTO dto) {
        DailyPaper dailyPaper = mapper.selectById(dto.getId());
        DailyPaper result;
        boolean dailyPaperStatusInvalid;
        Integer dailyPaperStatus;
        boolean doNotUpdateApprovalStatus;
        if (dailyPaper == null) {
            throw new ServiceException("信息异常，没有找到要修改的日报");
        }
        dailyPaperStatus = dailyPaper.getApprovalStatus();
        // 已通过部分不可编辑，但可新增工时（不满7个小时部分和加班部分）
        dailyPaperStatusInvalid =
                !(
                        WTB.getValue().equals(dailyPaperStatus) ||
                                WTJ.getValue().equals(dailyPaperStatus) ||
                                DSH.getValue().equals(dailyPaperStatus) ||
                                BTG.getValue().equals(dailyPaperStatus) ||
                                YTH.getValue().equals(dailyPaperStatus) ||
                                YTG.getValue().equals(dailyPaperStatus)
                );
        if (dailyPaperStatusInvalid) {
            throw new ServiceException("当前日报的状态不允许编辑");
        }
        Timestamp submissionTime = dailyPaper.getSubmissionTime();
        //更新操作  日报提交,且提交时间没有值时进行赋值
        if (dto.getSubmit() && !Optional.ofNullable(dailyPaper.getSubmissionTime()).isPresent()) {
            submissionTime = new Timestamp(System.currentTimeMillis());
        }

        result = tryCreateDailyPaper(dto, submissionTime);
        result.setId(dailyPaper.getId());
        // 如果保存不提交，且日报处于“已退回”或“不通过”，则不改变日报的状态（否则按照tryCreateDailyPaper中设置的，将日报状态设置为“未提交”）
//        doNotUpdateApprovalStatus = !dto.getSubmit() &&
//                (
//                        YTH.getValue().equals(dailyPaper.getApprovalStatus()) ||
//                                BTG.getValue().equals(dailyPaper.getApprovalStatus())
//                );
//        if (doNotUpdateApprovalStatus) {
//            // 将tryCreate...方法生成的新日报设置为日报原本的状态
//            result.setApprovalStatus(dailyPaper.getApprovalStatus());
//        }
        //修改将创建时间置空

        result.setCtime(dailyPaper.getCtime());
        BaseBuildEntityUtil.buildUpdate(result);


        return result;
    }

    /**
     * ~ 根据所给添加日报DTO获取日报条目（Map Key：“add”和“update”） ~
     *
     * @param dto        dto
     * @param dailyPaper 要添加或编辑的日报对象
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/24 11:28
     */
    private Map<String, List<DailyPaperEntry>> tryCreateDailyPaperEntryList(DailyPaperAddOrUpdateDTO dto, DailyPaper dailyPaper) {
        List<DailyPaperAddOrUpdateDTO.DailyPaperEntry> entries;
        List<DailyPaperEntry> resultForAdd;
        List<DailyPaperEntry> resultForUpdate;
        DailyPaperEntry entry;
        Long entryId;
        final Integer approvalStatus = dto.getSubmit()
                ? DSH.getValue()
                : WTJ.getValue();
        Integer oldApprovalStatus;

        entries = dto.getEntries();
        resultForAdd = Lists.newArrayListWithCapacity(entries.size());
        resultForUpdate = Lists.newArrayListWithCapacity(entries.size());
        for (DailyPaperAddOrUpdateDTO.DailyPaperEntry e : entries) {
            if (dto.getId() != null && e.getId() != null) {
                entryId = e.getId();
                entry = dailyPaperEntryMapper.selectById(entryId);
                if (ObjectUtil.isEmpty(entry)) {
                    throw new ServiceException("信息异常，日报条目不存在");
                }
                oldApprovalStatus = entry.getApprovalStatus();
                //已通过的跳过不做校验
                if (YTG.getValue().equals(oldApprovalStatus)) {
                    continue;
                }
                // 如果条目原本是已通过的，则原封不动将对象放入更新列表，不是已通过的才进行修改后再放入
                // 如果保存不提交，且原条目为“已退回”或“不通过”状态，则不改变原本状态
                entry = checkDailyPaper(e, dailyPaper).setApprovalStatus(approvalStatus);
                entry.setId(entryId);
                BaseBuildEntityUtil.buildUpdate(entry);
                distinguishOvertimeHoursType(entry);
                resultForUpdate.add(entry);
            } else {
                entry = checkDailyPaper(e, dailyPaper);
                distinguishOvertimeHoursType(entry);
                resultForAdd.add(entry.setApprovalStatus(approvalStatus));
            }
        }

        return ImmutableMap.of("add", resultForAdd, PARAM_UPDATE, resultForUpdate);
    }

    /**
     * 区分加班工时
     * @param entry
     */
    private void  distinguishOvertimeHoursType(DailyPaperEntry entry){
        //判断加班类型
        entry.setHolidayOvertimeHours(BigDecimal.ZERO);
        entry.setWorkOvertimeHours(BigDecimal.ZERO);
        entry.setRestOvertimeHours(BigDecimal.ZERO);
        Integer holidayType = holidayMapper.findHolidayType(entry.getSubmissionDate());
        if(holidayType!=null&&holidayType==1){
            entry.setHolidayOvertimeHours(entry.getAddedHours());
        }else if(holidayType!=null&&holidayType==0){
            entry.setRestOvertimeHours(entry.getAddedHours());
        }else{
            entry.setWorkOvertimeHours(entry.getAddedHours());
        }
    }


    /**
     * 新增编辑校验
     *
     * @param e
     * @param dailyPaper
     * @return
     */
    private DailyPaperEntry checkDailyPaper(DailyPaperAddOrUpdateDTO.DailyPaperEntry e, DailyPaper dailyPaper) {
        Long projectId = e.getProjectId();
        ProjectInfo project = projectInfoMapper.selectById(projectId);
        if (project == null) {
            throw new ServiceException("信息异常，未找到目标项目");
        }
        // 新旧任务区分校验
        TaskBo taskBo = validateAndGetTask(true, e.getOldTaskFlag(), projectId, e.getTaskId());
        return createDailyPaperEntry(project, taskBo, dailyPaper, e);
    }

    /**
     * ~ 根据所给添加日报DTO获取明日计划条目（Map Key：“add”和“update”）+ 明日计划 ~
     *
     * @param dto        dto
     * @param dailyPaper 要添加或编辑的日报对象
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaperEntry>
     * <AUTHOR>
     * @date 20223/2/07 16:11
     */
    private Map<String, List<TomorrowPlanPaperEntry>> tryCreateTomorrowPlanPaperEntryList(DailyPaperAddOrUpdateDTO dto, DailyPaper dailyPaper) {
        List<DailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry> tomorrowPlanPaperEntrys;
        ProjectInfo project;
        Long projectId;

        List<TomorrowPlanPaperEntry> tomorrowPlanResultForAdd;
        List<TomorrowPlanPaperEntry> tomorrowPlanResultForUpdate;
        TomorrowPlanPaperEntry tomorrowPlanPaperEntry;
        Long entryId;
        final Integer approvalStatus = dto.getSubmit()
                ? DSH.getValue()
                : WTJ.getValue();
        Integer oldApprovalStatus;
        boolean doNotUpdateApprovalStatus;
        // 明日计划
        TaskBo taskBo;
        tomorrowPlanPaperEntrys = dto.getTomorrowPlanPaperEntries();
        tomorrowPlanResultForAdd = Lists.newArrayListWithCapacity(tomorrowPlanPaperEntrys.size());
        tomorrowPlanResultForUpdate = Lists.newArrayListWithCapacity(tomorrowPlanPaperEntrys.size());
        for (DailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry e : tomorrowPlanPaperEntrys) {
            projectId = e.getProjectId();
            project = projectInfoMapper.selectById(projectId);
            if (project == null) {
                throw new ServiceException("信息异常，未找到目标项目");
            }
            // 校验并获取任务实体类
            taskBo = validateAndGetTask(false, e.getOldTaskFlag(), projectId, e.getTaskId());
            if (dto.getId() != null && e.getId() != null) {
                entryId = e.getId();
                tomorrowPlanPaperEntry = tomorrowPlanPaperEntryMapper.selectById(entryId);
                if (tomorrowPlanPaperEntry == null) {
                    throw new ServiceException("明日计划填报信息异常，日报条目不存在");
                }
                oldApprovalStatus = tomorrowPlanPaperEntry.getApprovalStatus();
                doNotUpdateApprovalStatus = !dto.getSubmit() &&
                        (
                                YTH.getValue().equals(dailyPaper.getApprovalStatus()) ||
                                        BTG.getValue().equals(dailyPaper.getApprovalStatus())
                        );
                // 如果条目原本是已通过的，则原封不动将对象放入更新列表，不是已通过的才进行修改后再放入
                // 如果保存不提交，且原条目为“已退回”或“不通过”状态，则不改变原本状态
                if (!YTG.getValue().equals(oldApprovalStatus) && !doNotUpdateApprovalStatus) {
                    tomorrowPlanPaperEntry = createTomorrowPlanPaperEntry(project, taskBo, dailyPaper, e)
                            .setApprovalStatus(approvalStatus);
                    tomorrowPlanPaperEntry.setId(entryId);
                    BaseBuildEntityUtil.buildUpdate(tomorrowPlanPaperEntry);
                }
                tomorrowPlanResultForUpdate.add(tomorrowPlanPaperEntry);
            } else {
                tomorrowPlanPaperEntry = createTomorrowPlanPaperEntry(project, taskBo, dailyPaper, e);
                tomorrowPlanResultForAdd.add(tomorrowPlanPaperEntry.setApprovalStatus(approvalStatus));
            }
        }


        return ImmutableMap.of("add", tomorrowPlanResultForAdd, PARAM_UPDATE, tomorrowPlanResultForUpdate);
    }

    private TaskBo validateAndGetTask(Boolean isDailyPaper, Integer oldTaskFlag, Long projectId, Long taskId) {
        // 取消新旧任务判断（新旧任务整合到新任务表）
        ProjectTaske projectTaske = projectTaskeMapper.selectById(taskId);
        if (!Optional.ofNullable(projectTaske).isPresent()) {
            throw new ServiceException("信息异常，未找到目标任务");
        }
        if (!projectTaske.getProjectId().equals(projectId)) {
            throw new ServiceException("信息异常，目标项目与任务不匹配");
        }
        if (ProjectTaskStateEnum.FINISHED.getValue().equals(projectTaske.getState())) {
            if (Boolean.TRUE.equals(isDailyPaper)) {
                throw new ServiceException("操作失败，“" + projectTaske.getTitle() + "”任务已关闭，无法继续填写工时");
            } else {
                throw new ServiceException("操作失败，“" + projectTaske.getTitle() + "”任务已关闭，无法继续填写明日计划");
            }
        }
        return TaskBo.form(projectTaske);
    }

    private DailyPaperEntry createDailyPaperEntry(
            ProjectInfo project,
            TaskBo task,
            DailyPaper dailyPaper,
            DailyPaperAddOrUpdateDTO.DailyPaperEntry dtoEntry
    ) {
        // 设置工时类型，从任务类型获取
        dtoEntry.setWorkType(task.getKind());
        // 设置旧任务标识（已取消旧任务，默认新任务）
        dtoEntry.setOldTaskFlag(YesOrNoEnum.NO.getValue());

        DailyPaperEntry result = new DailyPaperEntry();
        PigxUser user = SecurityUtils.getUser();
        Long deptId;
        Roster roster;

        log.info("填报获取用户{}", JSON.toJSON(user));
        result.setProjectId(project.getId());
        result.setProjectName(project.getItemName());
        result.setDailyPaperId(dailyPaper.getId());
        result.setSubmissionDate(dailyPaper.getSubmissionDate());
        result.setNormalHours(dtoEntry.getNormalHours());
        result.setAddedHours(dtoEntry.getAddedHours());
        result.setDescription(dtoEntry.getDescription());
        result.setApprovalReason("");
        result.setUserId(user.getId());
        result.setUserRealName(user.getName());
        deptId = user.getDeptId();
        if (deptId == null) {
            roster = rosterMapper.selectById(user.getId());
            deptId = roster.getDeptId();
        }
        result.setUserDeptId(deptId);
        result.setWorkType(dtoEntry.getWorkType());
        result.setOldTaskFlag(dtoEntry.getOldTaskFlag());
        result.setTaskId(task.getTaskId());
        result.setTaskName(task.getTaskName());

        BaseBuildEntityUtil.buildInsert(result);

        return result;
    }

    private TomorrowPlanPaperEntry createTomorrowPlanPaperEntry(
            ProjectInfo project,
            TaskBo task,
            DailyPaper dailyPaper,
            DailyPaperAddOrUpdateDTO.TomorrowPlanPaperEntry dtoEntry
    ) {
        // 设置工时类型，从任务类型获取
        dtoEntry.setWorkType(task.getKind());
        // 设置旧任务标识（已取消旧任务，默认新任务）
        dtoEntry.setOldTaskFlag(YesOrNoEnum.NO.getValue());

        TomorrowPlanPaperEntry result = new TomorrowPlanPaperEntry();
        PigxUser user = SecurityUtils.getUser();
        Roster roster;
        Long deptId;

        result.setProjectId(project.getId());
        result.setProjectName(project.getItemName());
        result.setDailyPaperId(dailyPaper.getId());
        result.setSubmissionDate(dailyPaper.getSubmissionDate());
        result.setDescription(dtoEntry.getDescription());
        result.setApprovalReason("");
        result.setUserId(user.getId());
        result.setUserRealName(user.getName());
        deptId = user.getDeptId();
        if (deptId == null) {
            roster = rosterMapper.selectById(user.getId());
            deptId = roster.getDeptId();
        }
        result.setUserDeptId(deptId);
        result.setWorkType(dtoEntry.getWorkType());
        result.setTaskId(task.getTaskId());
        result.setTaskName(task.getTaskName());
        result.setOldTaskFlag(dtoEntry.getOldTaskFlag());
        BaseBuildEntityUtil.buildInsert(result);

        return result;
    }

    @Override
    public List<DailyPaperVO> listDailyPaperMonthly(DailyPaperMonthlyDTO request) {
        LocalDate startDate = LocalDateTimeUtil.parseDate(request.getStartDate() + "-01", "yyyy-MM-dd");
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
        Long userId = SecurityUtils.getUser().getId();
        String projectNameLike = request.getProjectName();
        Integer approvalTab = request.getApprovalStatusTab();
        boolean queryAbnormal = EnumUtils.valueEquals(approvalTab, YC);
        List<Integer> approvalList = request.getApprovalStatusList();
        boolean queryUnFilled = approvalTab == null && CollUtil.contains(approvalList, WTB.getValue());
        // 查询月份是否归档
        boolean isFiled = EnumUtils.valueEquals(filingMapper.isFiledByDate(startDate), YesOrNoEnum.YES);
        // 查询假期数据
        Set<LocalDate> holidayDates = holidayMapper.findByDateRange(startDate, endDate);
        boolean useSearch = StringUtils.isNotBlank(projectNameLike) ||
                CollectionUtils.isNotEmpty(approvalList) ||
                approvalTab != null;
        // 查询oa的加班、请假数据
        List<OvertimeLeaveData> overtimeLeaveDataList = overtimeLeaveDataMapper.findByDateRangeAndUserId(
                startDate, endDate, userId
        );
        //查询OA项目调休数据
        List<CompensatoryLeaveDataVO> compensatoryLeaveDataDetailVOList
                = compensatoryLeaveDataService.findByDateTimeRangeAndUserId(startDate, endDate,userId);

        Map<LocalDate, BigDecimal> colcompensatoryLeaveDataMap=new HashMap<>();
        if(CollUtil.isNotEmpty(compensatoryLeaveDataDetailVOList)){
            colcompensatoryLeaveDataMap = compensatoryLeaveDataDetailVOList.stream()
                    .collect(Collectors.groupingBy(
                            CompensatoryLeaveDataVO::getBelongDate, // 分组依据
                            Collectors.reducing(BigDecimal.ZERO, // 初始值
                                    CompensatoryLeaveDataVO::getHourData, // 提取字段
                                    BigDecimal::add// 求和操作
                            )
                    ));
        }
        int olDataSize = overtimeLeaveDataList.size();
        Map<LocalDate, BigDecimal> dateAndOvertimeHourMap = Maps.newHashMapWithExpectedSize(olDataSize);
        Map<LocalDate, BigDecimal> dateAndLeaveHourMap = Maps.newHashMapWithExpectedSize(olDataSize);
        Stream<DailyPaperVO> resultStream;
        // 查询日报：通过当月开始时间、当月结束时间、用户id、项目名称
        List<DailyPaperVO> result;
        Set<LocalDate> datesHasApprovalStatusNotWTB;

        if (queryAbnormal) {
            approvalTab = null;
        }
        result = mapper.findBySubmissionDateRange(
                startDate, endDate, userId, projectNameLike, queryAbnormal ? null : approvalTab, approvalList
        );
        // 整理各日期的请假、加班数据
        overtimeLeaveDataList.forEach(ol -> {
            String type = ol.getType();
            LocalDate date = ol.getBelongdate();
            BigDecimal hours = ol.getHourData();

            if (EnumUtils.valueEquals(type, LeaveStatusEnum.XJ)) {
                dateAndLeaveHourMap.put(date, dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO).subtract(hours));
            } else if (EnumUtils.valueEquals(type, LeaveStatusEnum.JB)) {
                if (ol.getXmmc() == null) {
                    // 没有项目ID，说明加班工时没有挂靠在项目上，将其忽略
                    return;
                }
                dateAndOvertimeHourMap.put(date, dateAndOvertimeHourMap.getOrDefault(date, BigDecimal.ZERO).add(hours));
            } else {
                // 其他情况均为请假
                dateAndLeaveHourMap.put(date, dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO).add(hours));
            }
        });
        if (!useSearch) {
            // 如果用户没有使用搜索框，则将无日报的日子也填充上空日报信息
            fillPaperInHolidaysAndNones(
                    startDate, result, dateAndOvertimeHourMap, dateAndLeaveHourMap, holidayDates, isFiled, null
            );
        }
        if (queryUnFilled) {
            // 如果用户查询未填报的日报，则查询所有拥有非“未填报”状态日报的日期，并据此将缺失的日报补充上去
            datesHasApprovalStatusNotWTB = mapper.findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot(
                    userId, startDate, endDate, WTB.getValue()
            );
            fillPaperInHolidaysAndNones(
                    startDate,
                    result,
                    dateAndOvertimeHourMap,
                    dateAndLeaveHourMap,
                    holidayDates,
                    isFiled,
                    datesHasApprovalStatusNotWTB
            );
            // 进行一次过滤，排除节假日且未填报的日报
            result = result.stream()
                    .filter(x -> !(
                            holidayDates.contains(x.getSubmissionDate()) &&
                            EnumUtils.valueEquals(x.getApprovalStatus(), WTB)
                    ))
                    .collect(Collectors.toList());
        }
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer>  holidayMap
                = holidayList.stream()
                .filter(h-> h.getHolidayType()!=null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
        // 组装数据到日报对象、排序
        Map<LocalDate, BigDecimal> finalColcompensatoryLeaveDataMap = colcompensatoryLeaveDataMap;
        resultStream = result.stream()
                .sorted(Comparator.comparing(DailyPaperVO::getSubmissionDate, Comparator.reverseOrder()))
                .peek(x -> {
                    LocalDate submissionDate = x.getSubmissionDate();
                    BigDecimal hourData = dateAndOvertimeHourMap.getOrDefault(submissionDate, BigDecimal.ZERO);
                    BigDecimal leaveHourData = dateAndLeaveHourMap.getOrDefault(submissionDate, BigDecimal.ZERO);
                    BigDecimal  colcompensatoryLeaveData = finalColcompensatoryLeaveDataMap.getOrDefault(submissionDate,BigDecimal.ZERO);
                    List<Pair<DailyPaperAbnormalEnum, String>> abnormalInfo = dailyPaperAnalyzer.getAbnormalInfo(
                                    x.getSubmissionDate(),
                                    x.getApprovalStatus(),
                                    x.getWorkday(),
                                    x.getDailyHourCount(),
                                    leaveHourData.add(colcompensatoryLeaveData)
                            ).stream()
                            .filter(pair -> DailyPaperAbnormalEnum.SUBMIT == pair.getFirst())
                            .collect(Collectors.toList());
                    LocalDateTime ctime = x.getCtime();

                    if (abnormalInfo.isEmpty()) {
                        x.setIfAbnormal(false);
                        x.setAbnormalMsg(StringUtils.EMPTY);
                    } else {
                        x.setIfAbnormal(true);
                        x.setAbnormalMsg(abnormalInfo.get(0).getSecond());
                    }
                    x.setIfFiled(isFiled);
                    x.setIfHoliday(holidayDates.contains(submissionDate));
                    x.setLeaveHourData(leaveHourData);
                    x.setCtimeFormatted(x.getSubmissionTime() == null ? "" : DailyPaperDateUtils.asDatetimeString(x.getSubmissionTime()));
                    if (ObjectUtil.isEmpty(x.getMtime())) {
                        x.setMtime(ctime);
                    }
                    x.setWorkdayName(
                            BaseConstants.YES.equals(x.getWorkday()) && !WTB.getValue().equals(x.getApprovalStatus()) ?
                                    "正常提交" : "无日报"
                    );
                    x.setUserStatusName(EnumUtils.getNameByValue(PersonnelStatusEnum.class, x.getUserStatus()));
                    x.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(submissionDate));
                    x.setHolidayType(holidayMap.get(submissionDate));
                    x.setCompensatoryHourCount(colcompensatoryLeaveData);
                    //假期若未填写日报，状态放空处理
                    if (BaseConstants.YES.equals(x.getWorkday()) || !WTB.getValue().equals(x.getApprovalStatus())) {
                        x.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, x.getApprovalStatus()));
                    }
                    leaveHourData = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHourData) < 0 ?
                            BigDecimalUtils.SEVEN_DECIMAL : leaveHourData;
                    x.setHourData(hourData);
                    x.setTotalHour(x.getDailyHourCount().add(hourData).add(leaveHourData));
                    // 写入日期对应的多状态（若有）状态值
                    String approvalStatusStrings = x.getApprovalStatusStrings();
                    if (StringUtils.isNotBlank(approvalStatusStrings)) {
                        String[] split = approvalStatusStrings.split(",");
                        List<String> approvalStatusNameList = Arrays.stream(split)
                                .map(e -> EnumUtils.getNameByValue(ApprovalStatusEnum.class, Integer.parseInt(e)))
                                .collect(Collectors.toList());
                        x.setApprovalStatusNameList(approvalStatusNameList);
                    }
                    x.setProjectCount((x.getProjectCount()!=null&&x.getProjectCount()==0)?null:x.getProjectCount());
                    x.setTaskCount((x.getTaskCount()!=null&&x.getTaskCount()==0)?null:x.getTaskCount());
                    x.setDailyHourCount(getDataNull(x.getDailyHourCount()));
                    x.setAddedHourCount(getDataNull(x.getAddedHourCount()));
                    x.setHourData(getDataNull(x.getHourData()));
                    x.setCompensatoryHourCount(getDataNull(x.getCompensatoryHourCount()));
                    x.setLeaveHourData(getDataNull(x.getLeaveHourData()));

                });
        if (queryAbnormal) {
            // 如果查询的是异常日报，额外做一步过滤
            resultStream = resultStream.filter(DailyPaperVO::getIfAbnormal);
        }
        return resultStream.collect(Collectors.toList());
    }

    private BigDecimal  getDataNull(BigDecimal data) {
        return (data!=null&&(data.equals(BigDecimal.ZERO)
                ||data.equals(new BigDecimal("0.0"))
                ||data.equals(new BigDecimal("0.00"))))
                ?null:data;
    }

    /**
     * ~ 将日期月份剩余没有日报的天数填充进空日报，过滤掉无需填充的日报 ~
     *
     * @param papers              日报列表
     * @param startDate           日期
     * @param dateAndOvertimeMap  日期对应加班工时map
     * @param dateAndLeaveHourMap 日期对应请假工时map
     * @param holidayDates        节假日集合
     * @param ifFiled             月份数据是否已归档
     * @param datesExclude        无需填充的日期
     * <AUTHOR>
     */
    private void fillPaperInHolidaysAndNones(
            LocalDate startDate,
            List<DailyPaperVO> papers,
            Map<LocalDate, BigDecimal> dateAndOvertimeMap,
            Map<LocalDate, BigDecimal> dateAndLeaveHourMap,
            Set<LocalDate> holidayDates,
            boolean ifFiled,
            @Nullable Set<LocalDate> datesExclude
    ) {
        // 获取已存在日报的日期列表
        Set<LocalDate> existsDates = ImmutableSet.copyOf(
                papers.stream()
                        .map(DailyPaperVO::getSubmissionDate)
                        .collect(Collectors.toSet())
        );
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDate = now.toLocalDate();
        LocalDate date = startDate;
        int startMonth = startDate.getMonthValue();

        if (datesExclude == null) {
            datesExclude = ImmutableSet.of();
        }
        // 当日期≥当前时间，且日期对应月份=需添加空日报月份，进入循环
        while (!date.isAfter(nowDate) && date.getMonthValue() == startMonth) {
            // 当日期不包含在已存在日报的日期列表时，进行日报填充
            if (!existsDates.contains(date) && !datesExclude.contains(date)) {
                BigDecimal hourData = dateAndOvertimeMap.getOrDefault(date, BigDecimal.ZERO);
                BigDecimal leaveHourData = dateAndLeaveHourMap.getOrDefault(date, BigDecimal.ZERO);

                leaveHourData = BigDecimalUtils.SEVEN_DECIMAL.compareTo(leaveHourData) < 0 ?
                        BigDecimalUtils.SEVEN_DECIMAL : leaveHourData;
                papers.add(
                        DailyPaperVO.builder()
                                .approvalStatus(WTB.getValue())
                                .submissionDate(date)
                                .submissionDateFormatted(DailyPaperDateUtils.asDateString(date))
                                .approvalStatusName(holidayDates.contains(date) ?  null : WTB.getName())
                                .hourData(hourData)
                                .leaveHourData(leaveHourData)
                                .totalHour(hourData.add(leaveHourData))
                                .ctimeFormatted(Strings.EMPTY)
                                .workdayName("无日报")
                                .projectCount(0)
                                .taskCount(0)
                                .dailyHourCount(BigDecimal.ZERO)
                                .addedHourCount(BigDecimal.ZERO)
                                .ifFiled(ifFiled)
                                .ifHoliday(holidayDates.contains(date))
                                .ifAbnormal(false)
                                .abnormalMsg(StringUtils.EMPTY)
                                .build()
                );
            }
            date = date.plusDays(1);
        }
    }

    @Override
    public DailyPapersStatisticMonthlyVO getStatisticMonthly(DailyPaperMonthlyDTO request) {
        List<DailyPaperVO> papers = listDailyPaperMonthly(request);

        return sumWorkHourForDailyPapers(papers);
    }

    /**
     * ~ 根据日报计算按月统计数据 ~
     *
     * @param papers          日报列表
     * @return com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO
     *
     * <AUTHOR>
     * @date 2022/11/1 15:36
     */
    private DailyPapersStatisticMonthlyVO sumWorkHourForDailyPapers(List<DailyPaperVO> papers) {
        int total = papers.size();
        int waitingReview = 0;
        int abnormal = 0;
        List<Pair<DailyPaperAbnormalEnum, String>> abnormalInfo;

        if (total < 1) {
            return DailyPapersStatisticMonthlyVO.of(0, 0, 0);
        }
        for (DailyPaperVO paper : papers) {
            if (paper.getIfAbnormal()) {
                abnormalInfo = dailyPaperAnalyzer.getAbnormalInfo(
                        paper.getSubmissionDate(),
                        paper.getApprovalStatus(),
                        paper.getWorkday(),
                        paper.getDailyHourCount(),
                        paper.getLeaveHourData()
                );
                for (Pair<DailyPaperAbnormalEnum, String> typeAndMsg : abnormalInfo) {
                    switch (typeAndMsg.getFirst()) {
                        case SUBMIT:
                            abnormal++;
                            break;
                        case WAITING_REVIEW:
                            waitingReview++;
                            break;
                    }
                }
            } else if (EnumUtils.valueEquals(paper.getApprovalStatus(), DSH)) {
                waitingReview++;
            }

        }

        return DailyPapersStatisticMonthlyVO.of(total, waitingReview, abnormal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatusIfNeeded(List<Long> dailyPaperIds, ApprovalStatusEnum targetStatus) {
        Map<Long, DailyPaper> dailyPapers =
                mapper.selectList(new QueryWrapper<DailyPaper>().in("id", dailyPaperIds))
                        .stream()
                        .collect(Collectors.toMap(BaseEntity::getId, paper -> paper));
        Multimap<Long, DailyPaperEntry> entries = HashMultimap.create(dailyPapers.size(), 5);
        boolean needUpdate;
        DailyPaper dailyPaper;
        List<DailyPaper> papersNeedUpdate = Lists.newArrayListWithCapacity(dailyPapers.size());

        if (dailyPapers.isEmpty()) {
            return;
        }
        if (targetStatus == BTG) {
            // 如果要更新的状态是“不通过”，则直接变更日报状态后结束
            dailyPapers.values()
                    .stream()
                    // 筛选出需要变更状态的日报
                    .filter(paper -> !targetStatus.getValue().equals(paper.getApprovalStatus()))
                    .forEach(paper -> {
                        paper.setApprovalStatus(targetStatus.getValue());
                        BaseBuildEntityUtil.buildUpdate(paper);
                        papersNeedUpdate.add(paper);
                    });
            if (!papersNeedUpdate.isEmpty()) {
                mapper.updateApprovalStatus(papersNeedUpdate);
            }

            return;
        } else if (targetStatus == YTH) {
            // 如果要更新的状态是“已退回”，则直接变更日报状态后结束
            dailyPapers.values()
                    .stream()
                    // 筛选出需要变更状态的日报（日报为“不通过”时，不更新状态）
                    .filter(paper -> {
                        Integer approvalStatus = paper.getApprovalStatus();

                        return !EnumUtils.valueEquals(approvalStatus, targetStatus) &&
                                !EnumUtils.valueEquals(approvalStatus, BTG);
                    })
                    .forEach(paper -> {
                        paper.setApprovalStatus(targetStatus.getValue());
                        BaseBuildEntityUtil.buildUpdate(paper);
                        papersNeedUpdate.add(paper);
                    });
            if (!papersNeedUpdate.isEmpty()) {
                mapper.updateApprovalStatus(papersNeedUpdate);
            }

            return;
        }
        // 整理“日报ID-条目Set”
        dailyPaperEntryMapper.selectList(
                new QueryWrapper<DailyPaperEntry>().in(
                        "daily_paper_id",
                        BaseEntityUtils.mapToIdList(dailyPapers.values())
                )
        ).forEach(entry -> entries.put(entry.getDailyPaperId(), entry));
        if (entries.isEmpty()) {
            return;
        }
        // 遍历“日报ID-条目Set”，更新所需
        for (Long dailyPaperId : entries.keySet()) {
            dailyPaper = dailyPapers.get(dailyPaperId);
            needUpdate = entries.get(dailyPaperId)
                    .stream()
                    .allMatch(entry -> targetStatus.getValue().equals(entry.getApprovalStatus()))
                    &&
                    !targetStatus.getValue().equals(dailyPaper.getApprovalStatus());
            if (needUpdate) {
                dailyPaper.setApprovalStatus(targetStatus.getValue());
                BaseBuildEntityUtil.buildUpdate(dailyPaper);
                papersNeedUpdate.add(dailyPaper);
            }
        }
        if (!papersNeedUpdate.isEmpty()) {
            mapper.updateApprovalStatus(papersNeedUpdate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoSubmitJob(String now) {
        LocalDate lastDay = getNowDate(now).minusDays(1);
        List<SysUserPmsCqVO> users = new ArrayList<>();
        R<List<SysUserOutVO>> userListByMultiParameterPms = centerUserService.getUserListByMultiParameterPms(new UserPmsDTO());
        if (userListByMultiParameterPms.getCode() != CommonConstants.SUCCESS) {
            throw new ServiceException("用户服务调用异常");
        }
        Map<Long, Roster> userIdRosterMap = rosterMapper.findUserIdMap(null);
        Optional<List<SysUserOutVO>> sysUserOutVOList = userListByMultiParameterPms.unpack();
        if (sysUserOutVOList.isPresent() && !sysUserOutVOList.get().isEmpty()) {
            for (SysUserOutVO sysUserOutVO : sysUserOutVOList.get()) {
                Roster roster = userIdRosterMap.get(sysUserOutVO.getUserId());
                SysUserPmsCqVO sysUserPmsCqVO;
                LocalDate startDate;
                Long deptId;

                if (roster == null) {
                    continue;
                }
                startDate = roster.getStartDate();
                deptId = roster.getDeptId();
                if ("1".equals(sysUserOutVO.getStatus()) || deptId == null || startDate == null || lastDay.isBefore(startDate)) {
                    /*
                     * 两种情况下，不生成该用户前一天的日报：
                     * 1. 用户已禁用（status=1）
                     * 2. 用户入职时间在前一天之后（用户今天才入职，所以不生成昨天的日报）
                     * 此外，对于找不到花名册数据的用户，默认给TA生成日报
                     */
                    continue;
                }
                sysUserPmsCqVO = SysUserPmsCqVO.of(sysUserOutVO, null);
                sysUserPmsCqVO.setName(sysUserOutVO.getUsername());
                sysUserPmsCqVO.setJobName(com.google.common.base.Strings.nullToEmpty(roster.getJob()));
                sysUserPmsCqVO.setDeptId(deptId);
                users.add(sysUserPmsCqVO);
            }
        }
        if (users.isEmpty()) {
            // 没有需要生成日报的用户
            return;
        }

        List<DailyPaper> paperNeedUpdate;
        List<DailyPaper> paperNeedInsert;
        List<DailyPaperEntry> entryNeedUpdate;
        // <userId, obj>
        Map<Long, DailyPaper> papers;

        paperNeedUpdate = Lists.newArrayListWithCapacity(users.size());
        entryNeedUpdate = Lists.newArrayListWithExpectedSize(users.size() * 2);
        paperNeedInsert = Lists.newArrayListWithCapacity(users.size());
        papers = mapper.findBySubmissionDateAndUserIds(
                        lastDay,
                        CollectionUtils.extractToList(users, "userId")
                )
                .stream()
                .collect(Collectors.toMap(DailyPaper::getUserId, p -> p));
        users.forEach(user -> {
            Long userId = user.getUserId();
            DailyPaper paper = papers.get(userId);
            Roster roster = userIdRosterMap.get(userId);

            if (paper == null) {
                paper = createBlankDailyPaper(user, roster, lastDay);
                paper.setCreator("定时任务");
                paperNeedInsert.add(paper);
            }
        });
        if (!paperNeedInsert.isEmpty()) {
            mapper.batchSave(paperNeedInsert);
        }
        if (!entryNeedUpdate.isEmpty()) {
            dailyPaperEntryMapper.batchUpdate(entryNeedUpdate);
            updateApprovalStatusIfNeeded(
                    CollectionUtils.extractToList(paperNeedUpdate, "id"),
                    DSH
            );
        }
        // 清理无用日报
        mapper.cleanUpUseless();
    }

    private LocalDate getNowDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return LocalDate.now();
        }

        try {
            return LocalDateTimeUtil.parseDate(dateStr, "yyyy-MM-dd");
        } catch (DateTimeParseException e) {
            return LocalDate.now();
        }
    }

    private DailyPaper createBlankDailyPaper(SysUserPmsCqVO user, Roster roster, LocalDate submissionDate) {
        return BaseBuildEntityUtil.buildInsert(
                new DailyPaper()
                        .setUserId(user.getUserId())
                        .setUserStatus(
                                roster == null ? PersonnelStatusEnum.ZS.getValue() :
                                        PersonnelStatusEnum.getByEmployeeStatusEnum(EnumUtils.getEnumByValue(
                                                EmployeeStatusEnum.class,
                                                roster.getEmployeeStatus()
                                        )).getValue()
                        )
                        .setUserDeptId(user.getDeptId())
                        .setSubmissionDate(submissionDate)
                        .setWorkday(holidayMapper.exists(submissionDate) ? BaseConstants.NO : BaseConstants.YES)
                        .setFillingState(NORMAL.getValue())
                        .setApprovalStatus(WTB.getValue())
                        .setProjectCount(0)
                        .setTaskCount(0)
                        .setDailyHourCount(BigDecimal.ZERO)
                        .setAddedHourCount(BigDecimal.ZERO)
        );
    }

}

