package com.gok.pboot.pms.service.impl;

import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 日报数据上下文 - 用于封装构建日报详情所需的所有数据
 * 
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@Builder
public class DailyPaperDataContext {
    
    /**
     * 日报条目列表
     */
    private List<DailyPaperEntry> entries;
    
    /**
     * 明日计划条目列表
     */
    private List<TomorrowPlanPaperEntry> tomorrowEntries;
    
    /**
     * 项目信息映射
     */
    private Map<Long, ProjectInDailyPaperEntry> projects;
    
    /**
     * 用户真实姓名
     */
    private String userRealName;
    
    /**
     * 请假小时数
     */
    private BigDecimal leaveHour;
    
    /**
     * 调休小时数
     */
    private BigDecimal compensatoryLeave;
    
    /**
     * 假期信息
     */
    private Holiday holiday;
}
