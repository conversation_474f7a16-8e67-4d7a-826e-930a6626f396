package com.gok.pboot.pms.controller;


import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.IDailyReviewService;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 日报审核前端控制器
 * @menu 日报审核前端控制器
 * @since 2022-08-31
 */
@Slf4j
@RestController
@RequestMapping("/dailyReview")
@RequiredArgsConstructor
public class DailyReviewController extends BaseController {

    private final IDailyReviewService service;

    /**
     * 未审核数
     * 1 日报审核（日期维度）未审核数
     * 2 日报审核（项目维度）未审核数
     * 3 复用交付工时未审核数
     *
     * @return Map<Integer, Integer>
     */
    @PreAuthorize("@pms.hasPermission('DAILY_AUDIT')")
    @GetMapping("/unauditedNum")
    public ApiResult<Map<String, Integer>> unauditedNum() {
        Long userId = SecurityUtils.getUser().getId();
        return service.unauditedNum(userId);
    }

    /**
     * 未审核数
     * 1 日报审核（日期维度）未审核数
     * 2 日报审核（项目维度）未审核数
     * 3 复用交付工时未审核数
     *
     * @return Map<Integer, Integer>
     */
    @Inner
    @Deprecated
    @PreAuthorize("@pms.hasPermission('DAILY_AUDIT')")
    @GetMapping("/innerUnauditedNum")
    public ApiResult<Map<String, Integer>> innerUnauditedNum(Long userId) {
        return service.unauditedNum(userId);
    }

    /**
     * 人才复用、交付人员工时一键审核
     *
     * @param filter 审核的数据参数
     * @return 审核状态
     */
    @PostMapping("/reuseAndDelivery/oneAudit")
    public ApiResult<String> reuseAndDeliveryOneAudit(@RequestBody DailyReviewReuseAndDeliveryViewDTO filter) {
        return service.reuseAndDeliveryOneAudit(filter);
    }

    /**
     * 复用交付工时审核 - 分页
     *
     * @param dto 分页条件
     * @return 分页数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DELIVERY')")
    @PostMapping("/reuseAndDelivery/page")
    public ApiResult<Page<DailyReviewReuseAndDeliveryProjectVO>> reuseAndDeliveryPage(
            @RequestBody DailyReviewReuseAndDeliveryProjectDTO dto
    ) {
        return service.reuseAndDeliveryPage(dto);
    }


    /**
     * 复用交付工时审核-查看日报- 统计
     *
     * @return {@link ApiResult<DailyReviewReuseAndDeliveryViewTotalVO>}
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DELIVERY')")
    @PostMapping("/reuseAndDelivery/view/total")
    public ApiResult<DailyReviewReuseAndDeliveryViewTotalVO> reuseAndDeliveryViewTotal(
            @RequestBody DailyReviewTotalDTO filter
    ) {
        return service.reuseAndDeliveryViewTotal(filter);
    }

    /**
     * 复用交付工时审核-查看日报-分页
     *
     * @param filter 分页条件
     * @return {@link ApiResult<Page<DailyReviewReuseAndDeliveryViewVO>>}
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DELIVERY')")
    @PostMapping("/reuseAndDelivery/view/page")
    public ApiResult<Page<DailyReviewReuseAndDeliveryViewVO>> reuseAndDeliveryViewPage(
            @RequestBody DailyReviewReuseAndDeliveryViewDTO filter
    ) {
        return service.reuseAndDeliveryViewPage(filter);
    }

    /**
     * 日报审核（项目维度）-查看日报-一键审核
     *
     * @param dailyPaperIds 日报id列表
     * @return 审核结果
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @GetMapping("/projectDimension/oneAudit")
    public ApiResult<String> projectDimensionOneAudit(@RequestParam String dailyPaperIds) {
        return service.projectDimensionOneAudit(dailyPaperIds);
    }

    /**
     * 日报审核（项目维度）-分页
     *
     * @param filter 查询 条件
     * @return 分页数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/projectDimension/page")
    public ApiResult<Page<DailyReviewProjectVO>> projectDimensionPage(@RequestBody DailyReviewDTO filter) {
        return service.projectDimensionPage(filter);
    }

    /**
     * 日报审核（项目维度）-查看日报-统计
     *
     * @param totalDto 项目
     * @return 统计数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/projectDimension/view/total")
    public ApiResult<DailyReviewProjectViewTotalVO> projectDimensionViewTotal(
            @RequestBody DailyReviewDTO totalDto
    ) {
        return service.projectDimensionViewTotal(totalDto);
    }

    /**
     * 日报审核（项目维度）-查看日报-分页
     *
     * @param dto 查询条件
     * @return 分页数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/projectDimension/view/page")
    public ApiResult<Page<DailyReviewProjectViewVO>> projectDimensionViewPage(@RequestBody DailyReviewDTO dto) {
        return service.projectDimensionViewPage(dto);
    }

    /**
     * 分页查询日报审核日期维度
     *
     * @param dailyReviewDTO 传参
     * @return 分页数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/findPage")
    public ApiResult<Page<DailyReviewDateVO>> dailyReviewFindPageVO(@RequestBody DailyReviewDTO dailyReviewDTO) {
        return service.dailyReviewFindPageVO(dailyReviewDTO);
    }


    /**
     * 分页查询当天日报项目维度的详情
     *
     * @param dailyProjectDetailsFindPageDTO
     * @return {@link ApiResult<Page<DailyProjectDetailsFindPageVO>>}
     * @customParam pageNumber 页号
     * @customParam pageSize 分页大小
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/dailyProjectDetails/findPage")
    public ApiResult<Page<DailyProjectDetailsFindPageVO>> dailyProjectDetailsFindPageVO( @RequestBody @Valid DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO) {
        return service.dailyProjectDetailsFindPageVO(dailyProjectDetailsFindPageDTO);
    }

    /**
     * 分页查询当天日报项目维度的统计数据
     * @param dailyProjectDetailsFindPageDTO
     * @return {@link ApiResult}<{@link DailyReviewFindPageVO}>
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/dailyProjectDetails/findTotal")
    public ApiResult<DailyProjectDetailsFindTotalVO> dailyProjectDetailsFindTotal(@RequestBody @Valid DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO) {
        return service.dailyProjectDetailsFindTotal(dailyProjectDetailsFindPageDTO);
    }

    /**
     * 改变 日报的审核状态
     *
     * @param changeApprovalStatusDTO
     * @return {@link ApiResult<Page<DailyProjectDetailsFindPageVO>>}
     * @customParam approvalStatus 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已审核）
     * @customParam id 日报条目Id
     */
    @GetMapping("/changeApprovalStatus")
    public ApiResult<String> changeApprovalStatus(@Valid ChangeApprovalStatusDTO changeApprovalStatusDTO) {
        return service.changeApprovalStatus(changeApprovalStatusDTO);
    }

    /**
     * 一键审核 -- 分页查询当天日报项目维度的详情
     *
     * @param dailyProjectDetailsFindPageDTO·
     * @return {@link ApiResult<String>}
     * @customParam taskName 任务名称
     * @customParam userRealName 提交人姓名
     * @customParam status 审核状态（0：未审核、1：已审核）
     * @customParam submissionDate 日期时间
     * @customParam projectId 项目Id
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DELIVERY')")
    @GetMapping("/oneAudit")
    public ApiResult<String> dailyProjectDetailsOneAudit(@Valid DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO) {
        return service.dailyProjectDetailsOneAudit(dailyProjectDetailsFindPageDTO);
    }

    /**
     * ~ 查询当前用户需要审核条目的数量 ~
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.util.Map < java.lang.String, java.lang.Integer>>
     * <AUTHOR>
     * @date 2022/9/27 14:07
     */
    @GetMapping("/countAudits")
    public ApiResult<Map<String, Integer>> countAudits() {
        // 小程序下标回显接口，现在已未使用，返回值置零
        //return ApiResult.success(ImmutableMap.of("count", service.countAuditsForCurrentUser()));
        return ApiResult.success(ImmutableMap.of("count", 0));
    }

    /**
     * 查询工时审核 人员维度 详情
     * @param dto 查询条件
     * @return 分页数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/personnelDimension/view/page")
    public ApiResult<Page<DailyReviewPersonnelViewVO>> dailyPersonnelViewFindPage(@RequestBody DailyReviewDTO dto) {
        return service.dailyPersonnelViewFindPage(dto);
    }

    /**
     * 查询工时审核 人员维度 统计
     * @param dto 查询条件
     * @return 统计数据
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/personnelDimension/view/total")
    public ApiResult<DailyReviewPersonnelTotalVO> dailyPersonnelFindTotal(@RequestBody DailyReviewDTO dto) {
        return service.dailyPersonnelFindTotal(dto);
    }

    /**
     * 查询工时审核 人员维度 部门人员树
     * @param dto 查询条件
     * @return 部门人员树
     */
    @PreAuthorize("@pms.hasPermission('DAILY_VERIFY_DATE')")
    @PostMapping("/personnelDimension/view/deptUserTree")
    public ApiResult<List<Tree<String>>> dailyPersonnelFindDeptUserTree(@RequestBody DailyReviewDTO dto) {
        return service.dailyPersonnelFindDeptUserTree(dto);
    }
}
