package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.domain.BusinessContact;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel;
import com.gok.pboot.pms.entity.dto.BusinessInfoDTO;
import com.gok.pboot.pms.entity.dto.BusinessProgressDTO;
import com.gok.pboot.pms.entity.vo.BusinessDataLogVO;
import com.gok.pboot.pms.entity.vo.BusinessInfoVO;
import com.gok.pboot.pms.entity.vo.BusinessProgressDetailsVO;
import com.gok.pboot.pms.entity.vo.BusinessProgressVO;
import com.gok.pboot.pms.service.IBusinessInfoService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商机管理 Controller
 *
 * <AUTHOR>
 * @date 2023/11/20
 * @menu 商机台账
 */
@RestController
@AllArgsConstructor
@RequestMapping("/business")
public class BusinessInfoController {

    private final IBusinessInfoService service;

    /**
     * 商机台账 - 根据字典类型查找字典项列表
     *
     * @param dictType 字典类型
     * @return {@link ApiResult}<{@link List}<{@link PmsDictItem}>>
     */
    @GetMapping("/dictItem/{dictType}")
    public ApiResult<List<PmsDictItem>> findDictItemByDictType(@PathVariable("dictType") String dictType) {
        return service.findDictItemByDictType(dictType);
    }

    /**
     * 根据类型查找具有二级字典项的字典项列表
     *
     * @param dictType 字典类型
     * @return {@link ApiResult}<{@link List}<{@link PmsDictItemMultiLevel}>>
     */
    @GetMapping("/dictItemMultiLevel/{dictType}")
    public ApiResult<List<PmsDictItemMultiLevel>> findDictItemMultiLevelByDictType(@PathVariable("dictType") String dictType) {
        return service.findDictItemMultiLevelByDictType(dictType);
    }

    /**
     * 商机台账 - 通过字典类型列表查找字典类型-字典项Map
     *
     * @param dictTypes 字典类型列表
     * @return {@link ApiResult}<{@link Map}<{@link String},{@link Collection}<{@link PmsDictItem}>>>
     */
    @GetMapping("/dictItemList/{dictTypes}")
    public ApiResult<Map<String, Collection<PmsDictItem>>> findDictMapByTypeList(@PathVariable("dictTypes") String dictTypes) {
        return service.findDictMapByTypeList(dictTypes);
    }

    /**
     * 商机台账 - 动态获取所有项目所在地列表
     *
     * @return {@link ApiResult}<{@link List}<{@link String}>>
     */
    @GetMapping("/info/projectLocations")
    public ApiResult<List<String>> findAllProjectLocation() {
        return service.findAllProjectLocation();
    }

    /**
     * 商机台账 - 分页
     *
     * @param dto 传入参数
     * @return {@link ApiResult}<{@link Page}<{@link BusinessInfoVO}>>
     */
    @PreAuthorize("@pms.hasPermission('BUSINESS_ACCOUNT')")
    @PostMapping("/info/findPage")
    public ApiResult<Page<BusinessInfoVO>> findBusinessInfoPage(@RequestBody BusinessInfoDTO dto) {
        return service.findBusinessInfoPage(dto);
    }

    /**
     * 商机台账 - 导出
     *
     * @param dto 传入参数
     * @return 导出文件
     */
    @PreAuthorize("@pms.hasPermission('BUSINESS_ACCOUNT')")
    @PostMapping("/info/export")
    @ResponseExcel(name = "商机台账表")
    public List<BusinessInfoVO> exportBusinessInfo(@RequestBody BusinessInfoDTO dto) {
        return service.exportBusinessInfo(dto);
    }

    /**
     * 商机详情 - 详细信息
     *
     * @param id 商机ID
     * @return {@link ApiResult}<{@link BusinessInfoVO}>
     */
    @GetMapping("/info/findOne/{id}")
    public ApiResult<BusinessInfoVO> findOne(@PathVariable("id") Long id) {
        return service.findOne(id);
    }

    /**
     * 商机详情 - 联系人列表
     *
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessContact}>>
     */
    @GetMapping("/info/findContact/{id}")
    public ApiResult<List<BusinessContact>> findContact(@PathVariable("id") Long id) {
        return service.findContact(id);
    }

    /**
     * 商机详情 - 单个项目的进展情况列表
     *
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessProgressVO}>>
     */
    @GetMapping("/info/findProgress/{id}")
    public ApiResult<List<BusinessProgressVO>> findProgressGroupByBusinessId(@PathVariable("id") Long id) {
        return service.findProgressGroupByBusinessId(id);
    }

    /**
     * 商机详情 - 数据日志
     *
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessDataLogVO}>>
     */
    @GetMapping("/info/findDataLog/{id}")
    public ApiResult<List<BusinessDataLogVO>> findDataLog(@PathVariable("id") Long id) {
        return service.findDataLog(id);
    }

    /**
     * 商机进展记录 - 分页
     *
     * @param dto 传入参数
     * @return {@link ApiResult}<{@link Page}<{@link BusinessProgressVO}>>
     */
    @PreAuthorize("@pms.hasPermission('BUSINESS_RECORDS')")
    @GetMapping("/progress/findPage")
    public ApiResult<Page<BusinessProgressVO>> findBusinessProgressPage(BusinessProgressDTO dto) {
        return service.findBusinessProgressPage(dto);
    }

    /**
     * 商机进展记录 - 导出
     *
     * @param dto 传入参数
     * @return {@link List}<{@link BusinessProgressVO}>
     */
    @PreAuthorize("@pms.hasPermission('BUSINESS_RECORDS')")
    @GetMapping("/progress/export")
    @ResponseExcel(name = "商机进展表")
    public List<BusinessProgressVO> exportBusinessProgress(BusinessProgressDTO dto) {
        return service.exportBusinessProgress(dto);
    }

    /**
     * 商机进展记录 - 详细信息
     *
     * @param id 商机进展 ID
     * @return {@link ApiResult}<{@link BusinessProgressDetailsVO}>
     */
    @GetMapping("/progress/findOne/{id}")
    public ApiResult<BusinessProgressDetailsVO> findOneProgress(@PathVariable("id") Long id) {
        return service.findOneProgress(id);
    }

    /**
     * 商机流程文件接口（获取文件对应的下载路径信息）
     *
     * @param requestId 请求id
     * @return {@link ApiResult}<{@link Object}>
     */
    @GetMapping("/progress/getFile/{requestId}")
    public ApiResult<Object> getProgressFile(@PathVariable("requestId") Long requestId) {
        return service.getProgressFile(requestId);
    }
}