package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.SubordinatesDailyPaperDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.IDailyPaperEntryService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;

/**
 * - 日报条目 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/9 10:06
 */
@RestController
@RequestMapping("dailyPaperEntry")
@AllArgsConstructor
public class DailyPaperEntryController {

    private final IDailyPaperEntryService service;

    /**
     * 获取日报条目联系人
     *
     * @param projectId 项目id
     * @param taskId 任务id
     * @return {@link ApiResult}<{@link DailyPaperEntryContactsVo}>
     */
    @GetMapping("/getContacts")
    public ApiResult<DailyPaperEntryContactsVo> getContacts(@RequestParam("projectId") Long projectId, @RequestParam("taskId") Long taskId) {
        return ApiResult.success(service.getContacts(projectId, taskId));
    }

    @GetMapping("/findPage")
    public ApiResult<Page<DailyPaperEntryVO>> findPage(
            PageRequest pageRequest, HttpServletRequest request
    ) {
        return ApiResult.success(service.findPage(pageRequest, PropertyFilters.get(request, true)));
    }


    @GetMapping("/exportExcel")
    @ResponseExcel
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_TASK_TIME')")
    public List<Object> exportExcel(
            @RequestParam("startDate") LocalDate startDate,
            @RequestParam("endDate") LocalDate endDate,
            HttpServletRequest request
    ) {
        return service.exportExcel(startDate, endDate, PropertyFilters.get(request, true));
    }

    /**
     * @create by yzs at 2023/5/11
     * @description:查询下级的日报 详细
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    @PostMapping("/findBySubordinate")
    public R<Page<DailyReviewProjectAuditPageVO>> findBySubordinate(Page page, @RequestBody SubordinatesDailyPaperDTO dto) {
        return R.ok(service.findBySubordinate(page, dto));
    }

    /**
     * @create by yzs at 2023/5/11
     * @description:统计下级工时
     * @param: dto
     * @return: com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO
     */
    @PostMapping("/findBySubordinateStatic")
    public R<SubordinatePaperEntryStaticStrVO> findBySubordinateStatic(@RequestBody SubordinatesDailyPaperDTO dto) {
        return R.ok(service.findBySubordinateStatic(dto));
    }

    /**
     * @create by yzs at 2023/5/12
     * @description:个人面板查询日报详情
     * @param: pageRequest 分页参数
     * @param: dto 搜索条件 startTime，endTime，projectName
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.gok.pboot.pms.entity.DailyPaperEntry>
     */
    @PostMapping("/findByPanel")
    public R<Page<DailPanelPaperPageVO>> findByPanel(Page page, @RequestBody SubordinatesDailyPaperDTO dto) {
        return R.ok(service.findByPanel(page, dto));
    }

    /**
     * 统计个人面板-日报详细-总计
     * @param dto 查询条件 startTime，endTime，projectName
     * @return {@link ApiResult<PanelProjectSituationAnalysisVO>}
     */
    @PostMapping("/analysis/total")
    public R<PanelProjectSituationAnalysisVO> analysisTotal(@RequestBody SubordinatesDailyPaperDTO dto){
        return service.analysisTotal(dto);
    }

    /**
     * 通过任务id获取日报分页
     *
     * @param pageRequest 分页对象
     * @param request 参数
     * @customParam filter_L_taskId 传入的任务id
     * @customParam filter_I_hourStatus 传入的工时状态（0待审核，1无效）
     * @return {@link ApiResult}<{@link Page}<{@link DailyPaperEntryVO}>>
     */
    @GetMapping("/findPageByTaskId")
    public ApiResult<Page<DailyPaperEntryVO>> findPageByTaskId(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findPageByTaskId(pageRequest, PropertyFilters.get(request, true)));
    }

}
