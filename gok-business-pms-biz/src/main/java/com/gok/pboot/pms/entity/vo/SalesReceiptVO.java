package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售收款计划VO
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 客户id
     */
    @ExcelIgnore
    private Long customerId;

    /**
     * 项目编码
     */
    @ColumnWidth(20)
    @ExcelProperty("项目编码")
    private String projectNo;

    /**
     * 项目名称
     */
    @ColumnWidth(20)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编码
     */
    @ColumnWidth(20)
    @ExcelProperty("合同编码")
    private String contractCode;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态
     */
    @ColumnWidth(20)
    @ExcelProperty("项目状态")
    private String projectStatusTxt;

    /**
     * 合同金额（含税）
     */
    @ColumnWidth(20)
    @ExcelProperty("合同金额（含税）")
    private String contractMoney;

    /**
     * 累计收款金额（含税）
     */
    @ColumnWidth(20)
    @ExcelProperty("累计收款金额（含税）")
    private String accumulatedAmount;

    /**
     * 累计收款比例
     */
    @ColumnWidth(20)
    @ExcelProperty("累计收款比例")
    private String collectionRatio;

    /**
     * 当前款项名称
     */
    @ColumnWidth(20)
    @ExcelProperty("当前款项金额")
    private String currentPaymentMoney;

    /**
     * 当前款项名称
     */
    @ColumnWidth(20)
    @ExcelProperty("当前款项名称")
    private String currentPaymentName;

    /**
     * 收款滞后天数
     */
    @ColumnWidth(20)
    @ExcelProperty("收款滞后天数")
    private String collectionDelayDays;

    /**
     * 预计收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("预计收款日期")
    private String expectedDate;

    /**
     * 预警等级
     */
    @ColumnWidth(20)
    @ExcelProperty("预警等级")
    private String warningLevel;

    /**
     * 销售负责
     */
    @ColumnWidth(20)
    @ExcelProperty("销售负责")
    private String salesmanUserName;

    /**
     * 项目经理人员姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("项目经理")
    private String managerUserName;

    /**
     * 客户名称
     */
    @ColumnWidth(20)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 合同名称
     */
    @ColumnWidth(30)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 合同签订日期
     */
    @ColumnWidth(20)
    @ExcelProperty("合同签订日期")
    private String signingDate;

    /**
     * 归属主体
     */
    @ExcelIgnore
    private String attributableSubject;

    /**
     * 归属主体
     */
    @ColumnWidth(20)
    @ExcelProperty("归属主体")
    private String attributableSubjectTxt;

    /**
     * 业务归属一级部门
     */
    @ColumnWidth(20)
    @ExcelProperty("业务归属一级部门")
    private String firstDepartment;

    /**
     * 业务归属二级部门
     */
    @ColumnWidth(20)
    @ExcelProperty("业务归属二级部门")
    private String secondDepartment;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;
}
