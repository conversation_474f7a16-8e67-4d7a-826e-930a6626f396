package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.DailyPaperAnalysisDTO;
import com.gok.pboot.pms.entity.dto.SaturationStatisticsDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.ISaturationStatisticsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/22
 * 工时饱和度统计
 * @menu 工时饱和度统计
 */
@Slf4j
@RestController
@RequestMapping("statistics")
@AllArgsConstructor
public class SaturationStatisticsController {
    private final ISaturationStatisticsService saturationStatisticsService;

    /**
     * 项目占比情况
     *
     * @param saturationStatisticsDTO 查询实体
     * @return {@link ApiResult<List<ProjectPercentVO>>}
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/analyse/percentage")
    public ApiResult<List<ProjectPercentVO>> projectPercentage(@RequestBody SaturationStatisticsDTO saturationStatisticsDTO) {
        return ApiResult.success(saturationStatisticsService.projectPercentage(saturationStatisticsDTO));
    }

    /**
     * Top10排行榜
     *
     * @param saturationStatisticsDTO
     * @return
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/analyse/getRanking")
    public ApiResult<List<ProjectPercentVO>> top10Ranking(@RequestBody SaturationStatisticsDTO saturationStatisticsDTO) {
        return ApiResult.success(saturationStatisticsService.top10Ranking(saturationStatisticsDTO));
    }

    /**
     * 工时饱和度 -工时饱和度： 获取以部门为主要排序的工时饱和度条目数据
     *
     * @param saturationStatisticsDTO
     * @return
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/getDeptDetailData")
    public ApiResult<SaturationAndTotalVO> getDeptDetailData(@RequestBody SaturationStatisticsDTO saturationStatisticsDTO) {
        return ApiResult.success(saturationStatisticsService.getDeptDetailData(saturationStatisticsDTO));
    }

    /**
     * 工时饱和度-详细数据： 获取以人员为主要排序的工时饱和度条目数据
     *
     * @param saturationStatisticsDTO
     * @return
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/getUserDetailData")
    public ApiResult<Page<SituationAnalysisVO>> getUserDetailData(@RequestBody SaturationStatisticsDTO saturationStatisticsDTO) {
        return ApiResult.success(saturationStatisticsService.getUserDetailData(saturationStatisticsDTO));
    }


    /**
     * 导出
     *
     * @param saturationStatisticsDTO 实体
     * @param response
     * @throws IOException
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/export")
    public void export(@RequestBody SaturationStatisticsDTO saturationStatisticsDTO, HttpServletResponse response) throws IOException {
        saturationStatisticsService.export(saturationStatisticsDTO, response);
    }

    /**
     * 工时饱和度审核工时总数+滞后提交人天总数
     *
     * @param dto 查询条件 startTime，endTime，projectName
     * @return {@link ApiResult<PanelProjectSituationAnalysisVO>}
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/analysis/total")
    public R<PaneSaturationAnalysisVO> analysisTotal(@RequestBody DailyPaperAnalysisDTO dto) {
        return saturationStatisticsService.analysisTotal(dto);
    }

    /**
     * 查询饱和度工时明细
     * @param page 分页
     * @param dto 请求参数
     * @return 工时明细
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @PostMapping("/findBySaturation")
    public ApiResult<Page<DailyReviewProjectAuditPageVO>> findBySaturation(
            Page<DailyReviewProjectAuditPageVO> page, @RequestBody DailyPaperAnalysisDTO dto
    ) {
        return ApiResult.success(saturationStatisticsService.findBySaturation(page, dto));
    }

    /**
     * 导出饱和度工时明细
     * @param dto 请求参数
     * @return 数据列表
     */
    @PreAuthorize("@pms.hasPermission('STATISTICAL_QUERY_HOURS_SATURATION')")
    @ResponseExcel
    @PostMapping("/exportBySaturation")
    public List<SaturationExportVO> exportBySaturation(@RequestBody DailyPaperAnalysisDTO dto) {
        return saturationStatisticsService.exportBySaturation(dto);
    }
}
