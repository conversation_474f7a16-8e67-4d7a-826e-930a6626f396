package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.excel.annotation.RequestExcel;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.pms.common.base.*;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyAllExcelDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyExcelDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.handler.ProjectWeeklyAllEventListener;
import com.gok.pboot.pms.handler.ProjectWeeklyEventListener;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.IProjectWeeklyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.websocket.server.PathParam;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 项目 前端控制器
 * @menu 项目周报表
 * @since 2023-07-11 17:05:26
 */
@RestController
@RequestMapping("/projectWeekly")
@Api("项目周报")
@Validated
public class ProjectWeeklyController extends BaseController {

    @Resource
    private IProjectWeeklyService projectWeeklyService;

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    /**
     * 分页查询周报
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectWeeklyVO}>>
     */
    @ApiOperation(value = "分页查询周报列表", notes = "分页查询周报列表")
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectWeeklyVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(projectWeeklyService.findPage(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 根据id 查询周报详情
     *
     * @param id 会议纪要 主键id
     * @return {@link ApiResult}<{@link Page}<{@link ProjectWeeklyVO}>>
     */
    @ApiOperation(value = "根据id查询周报详情", notes = "根据id查询周报详情")
    @GetMapping("/{id}")
    public ApiResult<ProjectWeeklyVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(projectWeeklyService.findById(id));
    }

    /**
     * 分页查询全部周报
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectWeeklyVO}>>
     */
    @ApiOperation(value = "分页查询全部周报", notes = "分页查询全部周报")
    @GetMapping("/findAllPage")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public ApiResult<Page<ProjectWeeklyVO>> findAllPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(projectWeeklyService.findAllPage(pageRequest, PropertyFilters.get(request, true)));
    }

    /**
     * 获取新增填报自动统计数据
     *
     * @param request 请求
     * @return {@link ApiResult}<{@link ProjectWeeklyPreSaveDataVo}>
     */
    @ApiOperation(value = "获取新增填报自动统计数据")
    @GetMapping("findPreSaveData")
    public ApiResult<ProjectWeeklyPreSaveDataVo> findPreSaveData(HttpServletRequest request) {
        return ApiResult.success(projectWeeklyService.findPreSaveData(PropertyFilters.get(request)));
    }

    /**
     * 保存or修改项目周报
     *
     * @param dto dto
     * @return {@link ApiResult}
     */
    @ApiOperation(value = "保存or修改项目周报", notes = "保存or修改项目周报")
    @PostMapping("/saveOrUpdate")
    public ApiResult<Boolean> save(@RequestBody @Validated ProjectWeeklyDTO dto) {
        return ApiResult.success(projectWeeklyService.saveOrUpdateProjectWeekly(dto));
    }

    /**
     * 删除项目周报
     *
     * @param id 周报id
     * @return {@link R}
     */
    @ApiOperation(value = "删除项目周报", notes = "删除项目周报")
    @DeleteMapping("/delete/{id}")
    public R delete(@PathVariable("id") Long id) {
        return R.ok(projectWeeklyService.deleteById(id));
    }


    /**
     * 项目周报 导出
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return 导出文件
     */
    @GetMapping("/export")
    @ApiOperation(value = "项目周报导出", notes = "项目周报导出")
    @ResponseExcel(name = "项目周报", timePrefix = "yyyy-MM-dd")
    public List<ProjectWeeklyVO> export(PageRequest pageRequest, HttpServletRequest request) {
        return projectWeeklyService.export(pageRequest, PropertyFilters.get(request));
    }

    /**
     * 全部项目周报导出
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return 导出文件
     */
    @GetMapping("/exportAll")
    @ApiOperation(value = "全部项目周报导出", notes = "全部项目周报导出")
    @ResponseExcel(name = "周报表", timePrefix = "yyyy-MM-dd")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public List<ProjectWeeklyAllVO> exportAll(PageRequest pageRequest, HttpServletRequest request) {
        return projectWeeklyService.exportAll(pageRequest, PropertyFilters.get(request));
    }


    /**
     * 项目周报导入
     *
     * @param projectId         项目id
     * @param projectName       项目名称
     * @param projectDepartment 业务归属部门
     * @param excelVOList       列表
     * @return {@link ApiResult}
     */
    @PostMapping("/import")
    @ApiOperation(value = "项目周报导入", notes = "项目周报导入")
    public ApiResult importProjectWeekly(@NotNull(message = "项目id不能为空") @PathParam("projectId") Long projectId,
                                         @NotBlank(message = "项目名称不能为空") @PathParam("projectName") String projectName,
                                         @NotBlank(message = "项目归属部门不能为空") @PathParam("projectDepartment") String projectDepartment,
                                         @RequestExcel(readListener = ProjectWeeklyEventListener.class, ignoreEmptyRow = true)
                                         List<ProjectWeeklyExcelDTO> excelVOList, BindingResult bindingResult) {
        Map<Long, ProjectInfo> projectInfoMap = projectInfoMapper.findAll().stream()
                .collect(Collectors.toMap(ProjectInfo::getId, p -> p, (a, b) -> a));
        return projectWeeklyService.importProjectWeekly(projectId, projectName, projectDepartment,
                excelVOList, bindingResult, projectInfoMap);

    }

    /**
     * 多项目 周报导入
     * @param excelVOList       列表
     * @return {@link ApiResult}
     */
    @PostMapping("/importAll")
    @ApiOperation(value = "全部项目周报导入", notes = "全部项目周报导入")
    public ApiResult importAllProjectWeekly(
            @RequestExcel(readListener = ProjectWeeklyAllEventListener.class, ignoreEmptyRow = true)
                    List<ProjectWeeklyAllExcelDTO> excelVOList, BindingResult bindingResult){
        return projectWeeklyService.importAllProjectWeekly(excelVOList, bindingResult);
    }

    /**
     * 分页查询已关注的周报
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectWeeklyVO}>>
     */
    @GetMapping("/findAllAttentionPage")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public ApiResult<Page<ProjectWeeklyVO>> findAllAttentionPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(projectWeeklyService.findAllAttentionPage(pageRequest, PropertyFilters.get(request,
                true)));
    }

    /**
     * 全部已关注周报导出
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return 导出文件
     */
    @GetMapping("/exportAllAttention")
    @ResponseExcel(name = "关注项目周报表", timePrefix = "yyyy-MM-dd")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public List<ProjectWeeklyAllVO> exportAllAttention(PageRequest pageRequest, HttpServletRequest request) {
        return projectWeeklyService.exportAllAttention(pageRequest, PropertyFilters.get(request));
    }

    /**
     * 获取全部、我的 关注新增的周报数量
     *
     * @param request {@link HttpServletRequest}
     * @return {@link ApiResult<ProjectWeekUnreadNumVO>}
     */
    @GetMapping("/getUnreadNum")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public ApiResult<ProjectWeekUnreadNumVO> getUnreadNum(HttpServletRequest request) {
        return ApiResult.success(projectWeeklyService.getUnreadNum(PropertyFilters.get(request, true)));
    }

    /**
     * 未提交项目数、正常提交项目数，滞后提交数查询接口
     * @param request 请求参数
     * @customParam filter_S_reportStart 汇报日期开始时间
     * @customParam filter_S_reportEnd 汇报日期结束时间
     * @return {@link ApiResult}<{@link WeeklySubmitNum}>}
     */
    @GetMapping("/submitNum")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public ApiResult<WeeklySubmitNum> submitNum(HttpServletRequest request){
        return ApiResult.success(projectWeeklyService.submitNum(PropertyFilters.get(request, true)));
    }

    /**
     * 未提交项目、正常提交项目，滞后提交项目 查询接口
     * @param pageRequest 分页参数
     * @param request 请求参数
     * @customParam filter_I_submitState 查询的状态
     * @customParam filter_S_reportStart 汇报日期开始时间
     * @customParam filter_S_reportEnd 汇报日期结束时间
     * @return {@link ApiResult}<{@link Page}<{@link WeeklySubmitInfo}>>}
     */
    @GetMapping("/submitInfo")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_WEEK')")
    public ApiResult<Page<WeeklySubmitInfo>> submitInfo(PageRequest pageRequest,HttpServletRequest request){
        return ApiResult.success(projectWeeklyService.submitInfo(pageRequest,PropertyFilters.get(request, true)));
    }
}
