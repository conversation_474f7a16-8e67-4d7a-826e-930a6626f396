package com.gok.pboot.pms.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.excel.handler.ListAnalysisEventListener;
import com.gok.components.excel.kit.Validators;
import com.gok.components.excel.vo.ErrorMessage;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyAllExcelDTO;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多项目周报导入监听器
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
@Slf4j
public class ProjectWeeklyAllEventListener extends ListAnalysisEventListener<ProjectWeeklyAllExcelDTO> {

    private final List<ProjectWeeklyAllExcelDTO> list = new ArrayList<>();

    private final List<ErrorMessage> errorMessageList = new ArrayList<>();

    private Long lineNum = 1L;

    @Override
    public void invoke(ProjectWeeklyAllExcelDTO excelData, AnalysisContext analysisContext) {

        lineNum++;
        log.debug("解析到第{}行数据:{}", lineNum, excelData);

        // 参数格式校验
        excelData.validate();
        Set<ConstraintViolation<ProjectWeeklyAllExcelDTO>> violations = Validators.validate(excelData);
        if (!violations.isEmpty()) {
            // 异常捕捉
            Set<String> messageSet = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.toSet());
            errorMessageList.add(new ErrorMessage(lineNum, messageSet));
        } else {
            list.add(excelData);
        }
    }

    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        log.info("解析到的表头数据: {}", headMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.debug("Excel read analysed");
        if (CollUtil.isNotEmpty(errorMessageList)) {
            log.error("Excel导入参数校验失败详情为:{}", errorMessageList);
        }
    }

    @Override
    public List<ProjectWeeklyAllExcelDTO> getList() {
        return list;
    }

    @Override
    public List<ErrorMessage> getErrors() {
        return errorMessageList;
    }

    @Override
    public void onException(Exception e, AnalysisContext analysisContext) {
        //  捕获其他异常进行处理
        if (!Optional.ofNullable(e.getMessage()).isPresent()) {
            log.error("第{}行数据保存失败", lineNum, e);
        }
        HashSet messageSet = new HashSet();
        messageSet.add(e.getMessage());
        errorMessageList.add(new ErrorMessage(lineNum, messageSet));
    }

}
