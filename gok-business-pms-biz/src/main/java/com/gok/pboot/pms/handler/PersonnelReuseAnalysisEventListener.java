package com.gok.pboot.pms.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.excel.handler.ListAnalysisEventListener;
import com.gok.components.excel.kit.Validators;
import com.gok.components.excel.vo.ErrorMessage;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 人才复用 PersonnelReuseAnalysisEventListener
 *
 * <AUTHOR>
 * @date 2022/9/1
 */
@Slf4j
public class PersonnelReuseAnalysisEventListener extends ListAnalysisEventListener<Object> {

	private final List<Object> list = new ArrayList<>();

	private final List<ErrorMessage> errorMessageList = new ArrayList<>();

	private Long lineNum = 1L;

	@Override
	public void invoke(Object o, AnalysisContext analysisContext) {

		lineNum++;

		if (lineNum == 2){
			return;
		}

		Set<ConstraintViolation<Object>> violations = Validators.validate(o);
		if (!violations.isEmpty()){
			Set<String> messageSet = violations.stream().map(ConstraintViolation::getMessage)
					.collect(Collectors.toSet());
			errorMessageList.add(new ErrorMessage(lineNum, messageSet));
		}
		else {
			list.add(o);
		}
	}

	@Override
	public void invokeHeadMap(Map headMap, AnalysisContext context) {
		log.info("解析到的表头数据: {}", headMap);
		list.add(headMap.get(0));
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
		log.debug("Excel read analysed");
	}

	@Override
	public List<Object> getList() {
		return list;
	}

	@Override
	public List<ErrorMessage> getErrors() {
		return errorMessageList;
	}

}
