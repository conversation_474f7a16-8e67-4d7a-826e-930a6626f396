package com.gok.pboot.pms.mapper;

import com.gok.pboot.pms.common.base.MapperHandler;
import com.gok.pboot.pms.entity.PersonnelDeliveryHour;
import com.gok.pboot.pms.entity.dto.ChangeApprovalStatusDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperCommonDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/10
 */
@Mapper
public interface PersonnelDeliveryHourMapper extends MapperHandler<PersonnelDeliveryHourPageVO> {

    /**
     * 批量更新
     *
     * @param update
     */
    void batchUpdate(@Param("list") List<PersonnelDeliveryHour> update);

    /**
     * 批量保存
     *
     * @param insert
     */
    void batchSave(@Param("list") List<PersonnelDeliveryHour> insert);

    /**
     * 按日期查找交付人员工时条目
     *
     * @param yearMonth
     * @return
     */
    List<PersonnelDeliveryHour> selectListByReuseDate(String yearMonth);

    int updateById(@Param("personnelReuseUpdateDTO") PersonnelReuseUpdateDTO personnelReuseUpdateDTO);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(List<Long> list);

    /**
     * 按id获取日报
     *
     * @param id id
     * @return 日报
     */
    DailyPaperCommonDTO selectDailyPaperById(Long id);

    /**
     * 修改交付人员的工时审批记录
     *
     * @param changeApprovalStatusDTO 审核数据
     */
    void updateApprovalStatusById(@Param("param") ChangeApprovalStatusDTO changeApprovalStatusDTO);

    void updateApprovalStatusByIds(@Param("auditName") String auditName, @Param("auditId") Long auditId,
                                   @Param("mTime") LocalDateTime mTime, @Param("reuseIds") List<Long> reuseIds);

    /**
     * 获取上月交付人员工时已导入项目ID
     *
     * @param date 上月日期
     * @return {@link List}<{@link Long}>
     */
    List<Long> getAlreadyImportProjectIds(@Param("date") LocalDate date);

    /**
     * 按工号、日期、项目ID查找交付人员工时条目
     *
     * @param workCodeList
     * @param dateList
     * @param projectIds
     * @return
     */
    List<PersonnelDeliveryHourPageVO> findByWorkCodeAndReuseDate(@Param("workCodeList") List<String> workCodeList,
                                                                 @Param("reuseDateList") List<String> dateList,
                                                                 @Param("projectIds") List<Long> projectIds);

}
