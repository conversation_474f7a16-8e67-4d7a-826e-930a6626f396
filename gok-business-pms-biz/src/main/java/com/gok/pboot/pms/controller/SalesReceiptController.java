package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.components.excel.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.SalesReceiptDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.AttributableSubjectEnum;
import com.gok.pboot.pms.enumeration.PaymentEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.enumeration.WarningLevelEnum;
import com.gok.pboot.pms.service.ISalesReceiptCollectionRecordsService;
import com.gok.pboot.pms.service.ISalesReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售收款计划
 *
 * <AUTHOR>
 * @menu 销售收款计划
 * @since 2023-09-27
 */
@RestController
@RequestMapping("/sales-receipt")
@RequiredArgsConstructor
@Api(tags = "销售收款计划")
public class SalesReceiptController {

    private final ISalesReceiptService service;

    private final ISalesReceiptCollectionRecordsService iSalesReceiptCollectionRecordsService;

    /**
     * 分页条件查询
     *
     * @param dto 查询条件
     * @return R
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页条件查询", notes = "分页条件查询")
    public R<Page<SalesReceiptVO>> findPage(@RequestBody @Valid SalesReceiptDTO dto) {
        return R.ok(service.findPage(dto));
    }

    /**
     * 导出excel（异步导出）
     *
     * @param dto dto
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(name = "销售收款计划")
    @ApiOperation(value = "导出数据", notes = "导出数据")
    public List export(@RequestBody SalesReceiptDTO dto) {
        return service.export(dto);
    }

    /**
     * 定时推送消息到门户
     *
     * @return {@link R<Boolean>}
     */
    @Inner(false)
    @PostMapping("pushMessage")
    @ApiOperation(value = "推送消息", notes = "推送消息")
    public R<Boolean> pushMessage() {
        return R.ok(service.pushMessage());
    }

    /**
     * 催款记录
     *
     * @param salesReceiptId 销售收款id
     * @return {@link R}<{@link List}<{@link SalesReceiptCollectionRecordsVO}>>
     */
    @GetMapping("/recordsList/{salesReceiptId}")
    @ApiOperation(value = "催款记录", notes = "催款记录")
    public R<List<SalesReceiptCollectionRecordsVO>> recordsList(@PathVariable("salesReceiptId") Long salesReceiptId) {
        return R.ok(iSalesReceiptCollectionRecordsService.recordsList(salesReceiptId));
    }

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@link R}
     */
    @PostMapping("/saveRecords")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    public R<String> saveRecords(@RequestBody @Valid SalesReceiptCollectionRecordsDTO dto) {
        return iSalesReceiptCollectionRecordsService.saveRecords(dto)
                ? R.ok("新增记录成功") : R.failed("新增记录失败");
    }

    /**
     * 页面列表查询字段
     *
     * @return {@link R}<{@link SalesQueryFormVO}>
     */
    @GetMapping("/dictItemList")
    @ApiOperation(value = "页面列表查询字段", notes = "页面列表查询字段")
    public R<SalesQueryFormVO> dictItemList() {
        SalesQueryFormVO.SalesQueryFormVOBuilder builder = SalesQueryFormVO.builder();
        // 1、项目状态
        List<ProjectStatusVO> projectStatusVoList = new ArrayList<>();
        for (ProjectStatusEnum p: ProjectStatusEnum.values()) {
            projectStatusVoList.add(new ProjectStatusVO(p.getValue(), p.getName()));
        }
        builder.projectStatusVoList(projectStatusVoList);
        // 2、预警等级
        List<WarningLevelVO> warningLevelVoList = new ArrayList<>();
        for (WarningLevelEnum w: WarningLevelEnum.values()) {
            warningLevelVoList.add(new WarningLevelVO(w.getValue(), w.getName()));
        }
        builder.warningLevelVoList(warningLevelVoList);
        // 3、款项名称
        List<PaymentVO> paymentVoList = new ArrayList<>();
        for (PaymentEnum p: PaymentEnum.values()) {
            paymentVoList.add(new PaymentVO(p.getValue(), p.getName()));
        }
        builder.paymentVoList(paymentVoList);
        // 4、归属主体
        List<AttributableSubjectVO> attributableSubjectVoList = new ArrayList<>();
        for (AttributableSubjectEnum a: AttributableSubjectEnum.values()) {
            attributableSubjectVoList.add(new AttributableSubjectVO(a.getName(), a.getName()));
        }
        builder.attributableSubjectVoList(attributableSubjectVoList);
        return R.ok(builder.build());
    }

}
